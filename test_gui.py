#!/usr/bin/env python3
"""
GUI界面测试脚本
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog, IndeterminateProgressDialog


def test_progress_dialog():
    """测试进度对话框"""
    root = tk.Tk()
    root.title("进度对话框测试")
    root.geometry("400x300")
    
    def test_determinate():
        """测试确定进度对话框"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def update_progress():
            for i in range(0, 101, 5):
                if dialog.is_cancelled:
                    break
                
                message = f"正在处理第 {i//5 + 1} 步"
                detail = f"当前进度: {i}%, 剩余时间: {(100-i)//5} 秒"
                
                dialog.update_progress(i, message, detail)
                time.sleep(0.5)
            
            if not dialog.is_cancelled:
                dialog.update_progress(100, "处理完成", "所有任务已完成！")
                time.sleep(1)
            
            dialog.close()
        
        thread = threading.Thread(target=update_progress)
        thread.daemon = True
        thread.start()
    
    def test_indeterminate():
        """测试不确定进度对话框"""
        dialog = IndeterminateProgressDialog(root)
        dialog.show()
        
        def update_progress():
            messages = [
                ("初始化系统", "正在加载配置文件..."),
                ("连接数据库", "正在建立数据库连接..."),
                ("验证数据", "正在验证输入数据的完整性..."),
                ("准备处理", "正在分配系统资源..."),
                ("执行计算", "正在执行复杂的数学运算..."),
                ("生成结果", "正在生成最终结果..."),
                ("清理资源", "正在清理临时文件..."),
            ]
            
            for i, (message, detail) in enumerate(messages):
                if dialog.is_cancelled:
                    break
                
                dialog.update_progress(0, message, detail)
                time.sleep(2)
            
            if not dialog.is_cancelled:
                dialog.update_progress(0, "处理完成", "所有任务已成功完成！")
                time.sleep(1)
            
            dialog.close()
        
        thread = threading.Thread(target=update_progress)
        thread.daemon = True
        thread.start()
    
    # 创建测试按钮
    frame = ttk.Frame(root, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(frame, text="进度对话框测试", font=("Arial", 16, "bold")).pack(pady=(0, 20))
    
    ttk.Button(
        frame, 
        text="测试确定进度对话框", 
        command=test_determinate,
        width=25
    ).pack(pady=10)
    
    ttk.Button(
        frame, 
        text="测试不确定进度对话框", 
        command=test_indeterminate,
        width=25
    ).pack(pady=10)
    
    ttk.Button(
        frame, 
        text="退出", 
        command=root.quit,
        width=25
    ).pack(pady=20)
    
    root.mainloop()


def test_main_window():
    """测试主窗口"""
    try:
        from gui.main_window import MainWindow
        
        app = MainWindow()
        app.run()
        
    except Exception as e:
        print(f"主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("=" * 50)
    print("GUI界面测试")
    print("=" * 50)
    
    choice = input("""
请选择测试项目:
1. 测试进度对话框
2. 测试主窗口
3. 退出

请输入选择 (1-3): """).strip()
    
    if choice == "1":
        print("启动进度对话框测试...")
        test_progress_dialog()
    elif choice == "2":
        print("启动主窗口测试...")
        test_main_window()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
