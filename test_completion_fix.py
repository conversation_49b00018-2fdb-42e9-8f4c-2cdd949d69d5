#!/usr/bin/env python3
"""
测试处理完成后取消按钮的修复
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog


def test_completion_cancel_fix():
    """测试处理完成后取消按钮的修复"""
    root = tk.Tk()
    root.title("处理完成后取消按钮修复测试")
    root.geometry("700x500")
    
    def start_completion_test():
        """开始完成测试"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def simulate_quick_completion():
            """模拟快速完成的处理"""
            try:
                steps = [
                    (10, "初始化", "正在初始化系统..."),
                    (30, "读取数据", "正在读取输入文件..."),
                    (50, "处理数据", "正在执行数据处理..."),
                    (70, "计算结果", "正在计算最终结果..."),
                    (90, "写入文件", "正在写入输出文件..."),
                    (100, "处理完成", "所有步骤已完成！")
                ]
                
                for percent, message, detail in steps:
                    if dialog.is_cancelled:
                        break
                    
                    dialog.update_progress(percent, message, detail)
                    time.sleep(0.8)  # 快速完成
                
                if not dialog.is_cancelled:
                    # 模拟处理器中的等待时间
                    time.sleep(1.5)
                    print("处理完成，对话框应该自动关闭")
                
                dialog.close()
                
            except Exception as e:
                print(f"测试出错: {e}")
                dialog.close()
        
        # 设置取消回调
        def on_cancel():
            print("用户尝试取消处理！")
        
        dialog.set_cancel_callback(on_cancel)
        
        # 在新线程中运行模拟处理
        thread = threading.Thread(target=simulate_quick_completion)
        thread.daemon = True
        thread.start()
    
    def start_slow_completion_test():
        """开始慢速完成测试"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def simulate_slow_completion():
            """模拟慢速完成的处理"""
            try:
                # 快速到达99%
                for i in range(0, 100, 10):
                    if dialog.is_cancelled:
                        break
                    dialog.update_progress(i, f"处理步骤 {i//10 + 1}", f"当前进度: {i}%")
                    time.sleep(0.2)
                
                if not dialog.is_cancelled:
                    # 在99%停留较长时间
                    dialog.update_progress(99, "即将完成", "正在进行最后的清理工作...")
                    time.sleep(3)  # 在99%停留3秒
                    
                    # 然后完成
                    dialog.update_progress(100, "处理完成", "所有工作已完成！")
                    time.sleep(1.5)  # 显示完成信息
                
                dialog.close()
                
            except Exception as e:
                print(f"测试出错: {e}")
                dialog.close()
        
        # 设置取消回调
        def on_cancel():
            print("用户在接近完成时尝试取消！")
        
        dialog.set_cancel_callback(on_cancel)
        
        # 在新线程中运行模拟处理
        thread = threading.Thread(target=simulate_slow_completion)
        thread.daemon = True
        thread.start()
    
    # 创建测试界面
    frame = ttk.Frame(root, padding="30")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame, 
        text="处理完成后取消按钮修复测试", 
        font=("Arial", 18, "bold")
    ).pack(pady=(0, 30))
    
    ttk.Label(
        frame,
        text="修复内容：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(0, 10))
    
    fixes = [
        "• 进度达到100%时立即禁用取消按钮",
        "• 取消按钮文本变为'处理完成'",
        "• 处理完成后1.5秒自动关闭对话框",
        "• 防止用户在完成后误点取消"
    ]
    
    for fix in fixes:
        ttk.Label(
            frame,
            text=fix,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    ttk.Label(
        frame,
        text="\n测试说明：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(20, 10))
    
    instructions = [
        "测试1 - 快速完成：观察进度达到100%时取消按钮是否立即禁用",
        "测试2 - 慢速完成：在99%时尝试点击取消，然后观察100%时的行为"
    ]
    
    for i, instruction in enumerate(instructions, 1):
        ttk.Label(
            frame,
            text=f"{i}. {instruction}",
            font=("Arial", 10),
            wraplength=600
        ).pack(anchor=tk.W, pady=2)
    
    # 测试按钮
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=(30, 0))
    
    ttk.Button(
        button_frame,
        text="测试1: 快速完成",
        command=start_completion_test,
        width=20
    ).pack(side=tk.LEFT, padx=(0, 10))
    
    ttk.Button(
        button_frame,
        text="测试2: 慢速完成",
        command=start_slow_completion_test,
        width=20
    ).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(
        button_frame,
        text="退出",
        command=root.quit,
        width=20
    ).pack(side=tk.LEFT, padx=(10, 0))
    
    root.mainloop()


def test_cancel_button_states():
    """测试取消按钮的各种状态"""
    root = tk.Tk()
    root.title("取消按钮状态测试")
    root.geometry("600x400")
    
    def test_normal_cancel():
        """测试正常取消"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def slow_task():
            for i in range(101):
                if dialog.is_cancelled:
                    print("任务被正常取消")
                    break
                dialog.update_progress(i, f"执行中 {i}%", f"步骤 {i}/100")
                time.sleep(0.1)
            dialog.close()
        
        dialog.set_cancel_callback(lambda: print("取消回调被调用"))
        threading.Thread(target=slow_task, daemon=True).start()
    
    def test_completion_cancel():
        """测试完成时的取消按钮"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def complete_task():
            for i in range(101):
                dialog.update_progress(i, f"执行中 {i}%", f"步骤 {i}/100")
                time.sleep(0.05)
            
            # 完成后等待，观察取消按钮状态
            time.sleep(3)
            dialog.close()
        
        dialog.set_cancel_callback(lambda: print("尝试在完成后取消"))
        threading.Thread(target=complete_task, daemon=True).start()
    
    # 创建测试界面
    frame = ttk.Frame(root, padding="30")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame, 
        text="取消按钮状态测试", 
        font=("Arial", 18, "bold")
    ).pack(pady=(0, 30))
    
    ttk.Label(
        frame,
        text="测试项目：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(0, 10))
    
    tests = [
        "1. 正常取消：在处理过程中点击取消按钮",
        "2. 完成时取消：观察进度100%时取消按钮的状态变化"
    ]
    
    for test in tests:
        ttk.Label(
            frame,
            text=test,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    # 按钮
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=(30, 0))
    
    ttk.Button(
        button_frame,
        text="测试正常取消",
        command=test_normal_cancel,
        width=20
    ).pack(side=tk.LEFT, padx=(0, 10))
    
    ttk.Button(
        button_frame,
        text="测试完成时取消",
        command=test_completion_cancel,
        width=20
    ).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(
        button_frame,
        text="退出",
        command=root.quit,
        width=20
    ).pack(side=tk.LEFT, padx=(10, 0))
    
    root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("处理完成后取消按钮修复测试")
    print("=" * 60)
    
    choice = input("""
请选择测试项目:
1. 测试处理完成后的取消按钮修复
2. 测试取消按钮的各种状态
3. 退出

请输入选择 (1-3): """).strip()
    
    if choice == "1":
        print("启动处理完成后取消按钮修复测试...")
        test_completion_cancel_fix()
    elif choice == "2":
        print("启动取消按钮状态测试...")
        test_cancel_button_states()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
