"""
数据处理模块
"""

# 延迟导入以避免依赖问题
def get_raster_io():
    from .raster_io import <PERSON><PERSON><PERSON>ead<PERSON>, RasterWriter
    return <PERSON><PERSON><PERSON>eader, RasterWriter

def get_vector_io():
    from .vector_io import VectorWriter
    return VectorWriter

def get_data_validator():
    from .data_validator import DataValidator
    return DataValidator

__all__ = [
    'get_raster_io',
    'get_vector_io',
    'get_data_validator'
]
