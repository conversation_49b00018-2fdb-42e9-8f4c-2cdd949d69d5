#!/usr/bin/env python3
"""
代码收集脚本 - 修复中文乱码版本
将项目中所有Python文件的代码合并到一个txt文件中，正确处理中文编码

使用方法:
python collect_code_fixed.py

输出文件: all_code_fixed.txt
"""

import os
import sys
import chardet
from pathlib import Path
from datetime import datetime


class CodeCollectorFixed:
    """代码收集器 - 修复版"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.output_file = self.project_root / "all_code_fixed.txt"
        
        # 需要排除的目录
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            '.vscode',
            'build',
            'dist',
            '.pytest_cache',
            'node_modules',
            '.idea'
        }
        
        # 需要排除的文件
        self.exclude_files = {
            'collect_code.py',
            'collect_code_fixed.py',  # 排除自己
        }
        
        # 支持的Python文件扩展名
        self.python_extensions = {'.py', '.pyw'}
    
    def should_exclude_dir(self, dir_path):
        """判断是否应该排除目录"""
        dir_name = dir_path.name
        return dir_name in self.exclude_dirs or dir_name.startswith('.')
    
    def should_exclude_file(self, file_path):
        """判断是否应该排除文件"""
        file_name = file_path.name
        
        # 排除特定文件
        if file_name in self.exclude_files:
            return True
        
        # 只处理Python文件
        if file_path.suffix not in self.python_extensions:
            return True
        
        return False
    
    def detect_file_encoding(self, file_path):
        """使用chardet检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                
            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            # 如果置信度太低，使用默认编码
            if confidence < 0.7:
                # 检查文件头的编码声明
                try:
                    first_lines = raw_data.decode('ascii', errors='ignore').split('\n')[:3]
                    for line in first_lines:
                        if 'coding' in line or 'encoding' in line:
                            import re
                            match = re.search(r'coding[=:]\s*([-\w.]+)', line)
                            if match:
                                declared_encoding = match.group(1).lower()
                                if declared_encoding in ['cp936', 'gbk', 'gb2312']:
                                    return 'gbk'
                                elif declared_encoding in ['utf-8', 'utf8']:
                                    return 'utf-8'
                                else:
                                    return declared_encoding
                except:
                    pass
                
                # 默认尝试常用编码
                return 'gbk' if any(b > 127 for b in raw_data[:100]) else 'utf-8'
            
            # 标准化编码名称
            if encoding.lower() in ['gb2312', 'cp936']:
                return 'gbk'
            elif encoding.lower() in ['utf-8-sig']:
                return 'utf-8-sig'
            else:
                return encoding.lower()
                
        except Exception as e:
            print(f"检测编码失败 {file_path}: {e}")
            return 'utf-8'
    
    def read_file_content(self, file_path):
        """读取文件内容，正确处理编码"""
        # 检测编码
        encoding = self.detect_file_encoding(file_path)
        
        # 尝试读取文件
        encodings_to_try = [encoding, 'gbk', 'utf-8', 'cp936', 'utf-8-sig']
        
        for enc in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=enc, errors='strict') as f:
                    content = f.read()
                    return content, enc
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception:
                continue
        
        # 如果都失败了，使用replace模式
        try:
            with open(file_path, 'r', encoding='gbk', errors='replace') as f:
                content = f.read()
                return f"# 注意：此文件可能存在编码问题\n{content}", 'gbk(replace)'
        except Exception as e:
            return f"# 无法读取文件: {e}\n", 'error'
    
    def get_python_files(self):
        """获取所有Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            root_path = Path(root)
            
            # 排除不需要的目录
            dirs[:] = [d for d in dirs if not self.should_exclude_dir(root_path / d)]
            
            for file in files:
                file_path = root_path / file
                
                if not self.should_exclude_file(file_path):
                    python_files.append(file_path)
        
        # 按路径排序
        python_files.sort(key=lambda x: str(x.relative_to(self.project_root)))
        
        return python_files
    
    def collect_code(self):
        """收集所有代码"""
        print("开始收集Python代码（修复中文编码）...")
        
        python_files = self.get_python_files()
        
        if not python_files:
            print("未找到Python文件!")
            return False
        
        print(f"找到 {len(python_files)} 个Python文件")
        
        # 创建输出内容
        output_lines = []
        
        # 添加头部信息
        output_lines.append("=" * 80)
        output_lines.append("北斗网格转换工具 - 完整源代码（修复中文编码）")
        output_lines.append("=" * 80)
        output_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output_lines.append(f"文件总数: {len(python_files)}")
        output_lines.append("=" * 80)
        output_lines.append("")
        
        # 添加文件列表
        output_lines.append("文件列表:")
        for i, file_path in enumerate(python_files, 1):
            rel_path = file_path.relative_to(self.project_root)
            output_lines.append(f"  {i:2d}. {rel_path}")
        output_lines.append("")
        output_lines.append("=" * 80)
        output_lines.append("")
        
        # 处理每个文件
        for i, file_path in enumerate(python_files, 1):
            rel_path = file_path.relative_to(self.project_root)
            print(f"处理文件 {i}/{len(python_files)}: {rel_path}")
            
            # 添加文件分隔符
            output_lines.append("=" * 80)
            output_lines.append(f"文件 {i}: {rel_path}")
            output_lines.append("=" * 80)
            
            # 读取文件内容
            content, used_encoding = self.read_file_content(file_path)
            
            # 添加编码信息
            if used_encoding != 'utf-8':
                output_lines.append(f"# 文件编码: {used_encoding}")
            output_lines.append("")
            
            # 添加行号
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                output_lines.append(f"{line_num:4d}: {line}")
            
            output_lines.append("")
            output_lines.append("")
        
        # 写入输出文件
        try:
            # 使用UTF-8 BOM编码，确保在Windows记事本中正确显示中文
            with open(self.output_file, 'w', encoding='utf-8-sig', newline='\n') as f:
                f.write('\n'.join(output_lines))
            
            print(f"代码收集完成!")
            print(f"输出文件: {self.output_file}")
            print(f"文件大小: {self.output_file.stat().st_size / 1024:.1f} KB")
            print(f"编码格式: UTF-8 with BOM (完美支持中文)")
            
            return True
            
        except Exception as e:
            print(f"写入文件失败: {e}")
            return False


def main():
    """主函数"""
    print("北斗网格转换工具 - 代码收集脚本（修复中文编码版）")
    print("=" * 60)
    
    # 检查是否安装了chardet
    try:
        import chardet
    except ImportError:
        print("需要安装chardet库来检测文件编码:")
        print("pip install chardet")
        sys.exit(1)
    
    collector = CodeCollectorFixed()
    
    try:
        # 收集代码
        success = collector.collect_code()
        
        if success:
            print("\n✅ 代码收集完成!")
            print("现在中文应该可以正确显示了！")
        else:
            print("\n❌ 代码收集失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
