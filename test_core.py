#!/usr/bin/env python3
"""
核心功能测试脚本 (不依赖地理空间库)
"""

import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.grid_calculator import BeidouGridCalculator
from core.aggregation import AggregationMethod, DataAggregator
from core.coordinate_utils import CoordinateUtils


def test_grid_calculator():
    """测试网格计算器"""
    print("测试网格计算器...")
    
    calculator = BeidouGridCalculator()
    
    # 测试网格编码计算
    lat, lon = 39.9042, 116.4074  # 北京天安门
    level = 7
    
    grid_code = calculator.calculate_grid_code(lat, lon, level)
    print(f"北京天安门 ({lat}, {lon}) 的{level}级网格编码: {grid_code}")
    
    # 测试网格尺寸
    lon_size, lat_size = calculator.get_grid_size(level)
    print(f"{level}级网格尺寸: 经度 {lon_size:.6f}°, 纬度 {lat_size:.6f}°")
    
    # 测试网格维度计算
    min_lon, min_lat = 116.0, 39.5
    max_lon, max_lat = 117.0, 40.5
    rows, cols = calculator.calculate_grid_dimensions(min_lon, min_lat, max_lon, max_lat, level)
    print(f"区域 ({min_lon}, {min_lat}) 到 ({max_lon}, {max_lat}) 的{level}级网格维度: {rows}x{cols}")
    
    # 测试不同级别
    for test_level in [1, 5, 10]:
        code = calculator.calculate_grid_code(lat, lon, test_level)
        size = calculator.get_grid_size(test_level)
        print(f"级别{test_level}: 编码={code}, 尺寸={size[0]:.6f}°x{size[1]:.6f}°")
    
    print("✓ 网格计算器测试通过\n")


def test_aggregation():
    """测试数据聚合"""
    print("测试数据聚合...")
    
    # 创建测试数据
    np.random.seed(42)  # 固定随机种子
    data = np.random.rand(20, 20) * 100
    data[10:15, 10:15] = np.nan  # 添加一些无效值
    
    print(f"原始数据形状: {data.shape}")
    print(f"原始数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
    print(f"有效值数量: {np.sum(~np.isnan(data))}")
    
    # 测试不同聚合方法
    methods_to_test = ["max", "min", "mean", "median", "sum"]
    
    for method in methods_to_test:
        try:
            aggregator = DataAggregator(method=method, window_size=3)
            result = aggregator.aggregate(data, nodata_value=np.nan)
            
            print(f"{method}聚合:")
            print(f"  结果形状: {result.shape}")
            print(f"  结果范围: {np.nanmin(result):.2f} - {np.nanmax(result):.2f}")
            print(f"  有效值数量: {np.sum(~np.isnan(result))}")
            
        except Exception as e:
            print(f"  {method}聚合失败: {e}")
    
    # 测试不同窗口大小
    print("\n测试不同窗口大小:")
    for window_size in [3, 5, 7]:
        aggregator = DataAggregator(method="mean", window_size=window_size)
        result = aggregator.aggregate(data[:10, :10])  # 小数据测试
        print(f"窗口大小{window_size}: 结果形状{result.shape}, 均值{np.nanmean(result):.2f}")
    
    print("✓ 数据聚合测试通过\n")


def test_coordinate_utils():
    """测试坐标工具"""
    print("测试坐标工具...")
    
    # 测试坐标格式转换
    lat_dd = 39.9042
    lon_dd = 116.4074
    
    print(f"原始坐标 (十进制度): {lat_dd}, {lon_dd}")
    
    # 转换为度分秒
    lat_dms = CoordinateUtils.degrees_to_dms(lat_dd)
    lon_dms = CoordinateUtils.degrees_to_dms(lon_dd)
    print(f"度分秒格式: {lat_dms}, {lon_dms}")

    # 转换回十进制度验证
    lat_dd_back = CoordinateUtils.dms_to_degrees(*lat_dms)
    lon_dd_back = CoordinateUtils.dms_to_degrees(*lon_dms)
    print(f"转换回十进制: {lat_dd_back:.6f}, {lon_dd_back:.6f}")
    
    # 验证精度
    lat_diff = abs(lat_dd - lat_dd_back)
    lon_diff = abs(lon_dd - lon_dd_back)
    print(f"转换误差: 纬度{lat_diff:.8f}°, 经度{lon_diff:.8f}°")
    
    assert lat_diff < 1e-6, f"纬度转换误差过大: {lat_diff}"
    assert lon_diff < 1e-6, f"经度转换误差过大: {lon_diff}"
    
    # 测试距离计算
    print("\n测试距离计算:")
    test_points = [
        ("北京", 39.9042, 116.4074),
        ("上海", 31.2304, 121.4737),
        ("广州", 23.1291, 113.2644),
        ("深圳", 22.5431, 114.0579)
    ]
    
    for i, (name1, lat1, lon1) in enumerate(test_points):
        for j, (name2, lat2, lon2) in enumerate(test_points):
            if i < j:  # 避免重复计算
                distance = CoordinateUtils.calculate_distance(lat1, lon1, lat2, lon2)
                print(f"{name1}到{name2}距离: {distance/1000:.2f} km")  # 转换为km
    
    # 测试边界计算
    print("\n测试边界计算:")
    lat, lon = 39.9042, 116.4074
    # 测试网格边界计算（使用网格级别而不是距离）
    for level in [5, 7, 9]:
        bounds = CoordinateUtils.calculate_bounds(lat, lon, level)
        print(f"级别{level}网格边界: {bounds}")

        # 验证边界合理性
        min_lon, min_lat, max_lon, max_lat = bounds
        assert min_lon < lon < max_lon, f"经度边界错误: {bounds}"
        assert min_lat < lat < max_lat, f"纬度边界错误: {bounds}"
    
    print("✓ 坐标工具测试通过\n")


def test_integration():
    """集成测试"""
    print("集成测试...")
    
    try:
        # 模拟完整的处理流程
        print("1. 初始化组件...")
        calculator = BeidouGridCalculator()
        aggregator = DataAggregator(method="mean", window_size=5)
        
        # 创建模拟数据
        print("2. 创建测试数据...")
        np.random.seed(123)
        data = np.random.rand(50, 50) * 100
        
        # 添加一些空间模式
        for i in range(10, 40):
            for j in range(10, 40):
                data[i, j] += 50 * np.exp(-((i-25)**2 + (j-25)**2) / 100)
        
        print(f"   数据形状: {data.shape}")
        print(f"   数据范围: {np.min(data):.2f} - {np.max(data):.2f}")
        
        # 数据聚合
        print("3. 执行数据聚合...")
        aggregated = aggregator.aggregate(data)
        print(f"   聚合后形状: {aggregated.shape}")
        print(f"   聚合后范围: {np.nanmin(aggregated):.2f} - {np.nanmax(aggregated):.2f}")
        
        # 网格编码计算
        print("4. 计算网格编码...")
        test_coords = [
            (39.9, 116.4),  # 北京
            (31.2, 121.5),  # 上海
            (23.1, 113.3),  # 广州
        ]
        
        for lat, lon in test_coords:
            for level in [5, 7, 9]:
                code = calculator.calculate_grid_code(lat, lon, level)
                print(f"   ({lat}, {lon}) 级别{level}: {code}")
        
        # 网格维度计算
        print("5. 计算网格维度...")
        bounds = (116.0, 39.0, 117.0, 40.0)  # 北京区域
        for level in [6, 7, 8]:
            rows, cols = calculator.calculate_grid_dimensions(*bounds, level)
            total_grids = rows * cols
            print(f"   级别{level}: {rows}x{cols} = {total_grids}个网格")
        
        print("✓ 集成测试通过")
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        raise
    
    print()


def main():
    """主测试函数"""
    print("=" * 60)
    print("北斗网格转换工具 - 核心功能测试")
    print("=" * 60)
    print()
    
    try:
        test_grid_calculator()
        test_aggregation()
        test_coordinate_utils()
        test_integration()
        
        print("=" * 60)
        print("✓ 所有核心功能测试通过！")
        print("=" * 60)
        print()
        print("注意: 完整功能测试需要安装以下依赖:")
        print("  pip install rasterio fiona shapely pyproj")
        print()
        
    except Exception as e:
        print("=" * 60)
        print(f"✗ 测试失败: {e}")
        print("=" * 60)
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
