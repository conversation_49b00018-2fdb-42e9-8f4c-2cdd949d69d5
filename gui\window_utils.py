#!/usr/bin/env python3
"""
窗口工具函数
"""

import tkinter as tk
from typing import Union


def center_window(window: Union[tk.Tk, tk.Toplevel], width: int, height: int):
    """
    将窗口居中显示在屏幕上
    
    Args:
        window: 要居中的窗口对象
        width: 窗口宽度
        height: 窗口高度
    """
    # 获取屏幕尺寸
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    # 计算居中位置
    center_x = int(screen_width / 2 - width / 2)
    center_y = int(screen_height / 2 - height / 2)
    
    # 确保窗口不会超出屏幕边界
    center_x = max(0, center_x)
    center_y = max(0, center_y)
    
    # 设置窗口位置和大小
    window.geometry(f"{width}x{height}+{center_x}+{center_y}")


def center_window_on_parent(child: tk.Toplevel, parent: Union[tk.Tk, tk.Toplevel], width: int, height: int):
    """
    将子窗口居中显示在父窗口上
    
    Args:
        child: 子窗口对象
        parent: 父窗口对象
        width: 子窗口宽度
        height: 子窗口高度
    """
    # 更新父窗口以获取准确的位置和大小
    parent.update_idletasks()
    
    # 获取父窗口的位置和大小
    parent_x = parent.winfo_x()
    parent_y = parent.winfo_y()
    parent_width = parent.winfo_width()
    parent_height = parent.winfo_height()
    
    # 计算子窗口在父窗口中的居中位置
    center_x = parent_x + int(parent_width / 2 - width / 2)
    center_y = parent_y + int(parent_height / 2 - height / 2)
    
    # 获取屏幕尺寸以确保窗口不会超出屏幕
    screen_width = child.winfo_screenwidth()
    screen_height = child.winfo_screenheight()
    
    # 确保窗口不会超出屏幕边界
    center_x = max(0, min(center_x, screen_width - width))
    center_y = max(0, min(center_y, screen_height - height))
    
    # 设置子窗口位置和大小
    child.geometry(f"{width}x{height}+{center_x}+{center_y}")


def get_screen_size(window: Union[tk.Tk, tk.Toplevel]) -> tuple:
    """
    获取屏幕尺寸
    
    Args:
        window: 窗口对象
        
    Returns:
        (width, height): 屏幕宽度和高度
    """
    return window.winfo_screenwidth(), window.winfo_screenheight()


def get_optimal_window_size(screen_width: int, screen_height: int, 
                          min_width: int = 800, min_height: int = 600,
                          max_ratio: float = 0.8) -> tuple:
    """
    根据屏幕尺寸计算最佳窗口大小
    
    Args:
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
        min_width: 最小窗口宽度
        min_height: 最小窗口高度
        max_ratio: 窗口占屏幕的最大比例
        
    Returns:
        (width, height): 最佳窗口宽度和高度
    """
    # 计算基于屏幕比例的窗口大小
    optimal_width = int(screen_width * max_ratio)
    optimal_height = int(screen_height * max_ratio)
    
    # 确保不小于最小尺寸
    width = max(min_width, optimal_width)
    height = max(min_height, optimal_height)
    
    return width, height


def set_window_icon(window: Union[tk.Tk, tk.Toplevel], icon_path: str):
    """
    设置窗口图标
    
    Args:
        window: 窗口对象
        icon_path: 图标文件路径
    """
    try:
        window.iconbitmap(icon_path)
    except Exception:
        # 如果设置图标失败，忽略错误
        pass


def make_window_modal(dialog: tk.Toplevel, parent: Union[tk.Tk, tk.Toplevel]):
    """
    将对话框设置为模态
    
    Args:
        dialog: 对话框窗口
        parent: 父窗口
    """
    dialog.transient(parent)
    dialog.grab_set()
    dialog.focus_set()


def configure_window_properties(window: Union[tk.Tk, tk.Toplevel], 
                              title: str,
                              width: int, 
                              height: int,
                              min_width: int = None,
                              min_height: int = None,
                              resizable: bool = True,
                              center: bool = True,
                              icon_path: str = None):
    """
    配置窗口的基本属性
    
    Args:
        window: 窗口对象
        title: 窗口标题
        width: 窗口宽度
        height: 窗口高度
        min_width: 最小宽度
        min_height: 最小高度
        resizable: 是否可调整大小
        center: 是否居中显示
        icon_path: 图标文件路径
    """
    window.title(title)
    
    if center:
        center_window(window, width, height)
    else:
        window.geometry(f"{width}x{height}")
    
    if min_width and min_height:
        window.minsize(min_width, min_height)
    
    window.resizable(resizable, resizable)
    
    if icon_path:
        set_window_icon(window, icon_path)
