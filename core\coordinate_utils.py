"""
坐标转换工具模块
"""

import math
import numpy as np
from typing import Tuple, List, Optional


class CoordinateUtils:
    """坐标转换工具类"""
    
    @staticmethod
    def pixel_to_geo(pixel_x: int, pixel_y: int, transform: tuple) -> Tuple[float, float]:
        """
        像素坐标转地理坐标
        
        Args:
            pixel_x, pixel_y: 像素坐标
            transform: 仿射变换参数 (x_origin, pixel_width, 0, y_origin, 0, -pixel_height)
            
        Returns:
            (lon, lat): 地理坐标
        """
        x_origin, pixel_width, _, y_origin, _, pixel_height = transform
        
        lon = x_origin + pixel_x * pixel_width
        lat = y_origin + pixel_y * pixel_height
        
        return lon, lat
    
    @staticmethod
    def geo_to_pixel(lon: float, lat: float, transform: tuple) -> Tuple[int, int]:
        """
        地理坐标转像素坐标
        
        Args:
            lon, lat: 地理坐标
            transform: 仿射变换参数
            
        Returns:
            (pixel_x, pixel_y): 像素坐标
        """
        x_origin, pixel_width, _, y_origin, _, pixel_height = transform
        
        pixel_x = int((lon - x_origin) / pixel_width)
        pixel_y = int((lat - y_origin) / pixel_height)
        
        return pixel_x, pixel_y
    
    @staticmethod
    def calculate_bounds(center_lat: float, center_lon: float, 
                        grid_level: int) -> Tuple[float, float, float, float]:
        """
        根据中心点和网格级别计算边界
        
        Args:
            center_lat, center_lon: 中心点坐标
            grid_level: 网格级别
            
        Returns:
            (min_lon, min_lat, max_lon, max_lat): 边界坐标
        """
        from core.grid_calculator import BeidouGridCalculator
        
        calculator = BeidouGridCalculator()
        lon_size, lat_size = calculator.get_grid_size(grid_level)
        
        min_lon = center_lon - lon_size / 2
        max_lon = center_lon + lon_size / 2
        min_lat = center_lat - lat_size / 2
        max_lat = center_lat + lat_size / 2
        
        return min_lon, min_lat, max_lon, max_lat
    
    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """
        验证坐标是否有效
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            是否有效
        """
        return -90 <= lat <= 90 and -180 <= lon <= 180
    
    @staticmethod
    def normalize_longitude(lon: float) -> float:
        """
        标准化经度到[-180, 180]范围
        
        Args:
            lon: 经度
            
        Returns:
            标准化后的经度
        """
        while lon > 180:
            lon -= 360
        while lon < -180:
            lon += 360
        return lon
    
    @staticmethod
    def calculate_distance(lat1: float, lon1: float, 
                          lat2: float, lon2: float) -> float:
        """
        计算两点间的大圆距离（米）
        
        Args:
            lat1, lon1: 第一个点的坐标
            lat2, lon2: 第二个点的坐标
            
        Returns:
            距离（米）
        """
        # 地球半径（米）
        R = 6371000
        
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Haversine公式
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    @staticmethod
    def calculate_grid_area(lat: float, grid_level: int) -> float:
        """
        计算指定纬度和网格级别的网格面积（平方米）
        
        Args:
            lat: 纬度
            grid_level: 网格级别
            
        Returns:
            面积（平方米）
        """
        from core.grid_calculator import BeidouGridCalculator
        
        calculator = BeidouGridCalculator()
        lon_size, lat_size = calculator.get_grid_size(grid_level)
        
        # 地球半径（米）
        R = 6371000
        
        # 转换为弧度
        lat_rad = math.radians(lat)
        lon_size_rad = math.radians(lon_size)
        lat_size_rad = math.radians(lat_size)
        
        # 计算面积
        area = R**2 * lon_size_rad * lat_size_rad * math.cos(lat_rad)
        
        return abs(area)
    
    @staticmethod
    def create_grid_polygon(center_lat: float, center_lon: float, 
                           grid_level: int) -> List[Tuple[float, float]]:
        """
        创建网格多边形的顶点坐标
        
        Args:
            center_lat, center_lon: 中心点坐标
            grid_level: 网格级别
            
        Returns:
            多边形顶点坐标列表
        """
        min_lon, min_lat, max_lon, max_lat = CoordinateUtils.calculate_bounds(
            center_lat, center_lon, grid_level)
        
        # 返回矩形的四个顶点（逆时针）
        return [
            (min_lon, min_lat),  # 左下
            (max_lon, min_lat),  # 右下
            (max_lon, max_lat),  # 右上
            (min_lon, max_lat),  # 左上
            (min_lon, min_lat)   # 闭合
        ]
    
    @staticmethod
    def degrees_to_dms(degrees: float) -> Tuple[int, int, float]:
        """
        度转度分秒
        
        Args:
            degrees: 度数
            
        Returns:
            (度, 分, 秒)
        """
        abs_degrees = abs(degrees)
        d = int(abs_degrees)
        m = int((abs_degrees - d) * 60)
        s = ((abs_degrees - d) * 60 - m) * 60
        
        if degrees < 0:
            d = -d
        
        return d, m, s
    
    @staticmethod
    def dms_to_degrees(degrees: int, minutes: int, seconds: float) -> float:
        """
        度分秒转度
        
        Args:
            degrees: 度
            minutes: 分
            seconds: 秒
            
        Returns:
            度数
        """
        result = abs(degrees) + minutes/60 + seconds/3600
        return result if degrees >= 0 else -result
    
    @staticmethod
    def format_coordinate(lat: float, lon: float, format_type: str = "decimal") -> str:
        """
        格式化坐标显示
        
        Args:
            lat: 纬度
            lon: 经度
            format_type: 格式类型 ("decimal", "dms", "dm")
            
        Returns:
            格式化后的坐标字符串
        """
        if format_type == "decimal":
            return f"{lat:.6f}, {lon:.6f}"
        
        elif format_type == "dms":
            lat_d, lat_m, lat_s = CoordinateUtils.degrees_to_dms(lat)
            lon_d, lon_m, lon_s = CoordinateUtils.degrees_to_dms(lon)
            
            lat_dir = "N" if lat >= 0 else "S"
            lon_dir = "E" if lon >= 0 else "W"
            
            return (f"{abs(lat_d)}°{lat_m}'{lat_s:.2f}\"{lat_dir}, "
                   f"{abs(lon_d)}°{lon_m}'{lon_s:.2f}\"{lon_dir}")
        
        elif format_type == "dm":
            lat_d = int(lat)
            lat_m = (lat - lat_d) * 60
            lon_d = int(lon)
            lon_m = (lon - lon_d) * 60
            
            lat_dir = "N" if lat >= 0 else "S"
            lon_dir = "E" if lon >= 0 else "W"
            
            return (f"{abs(lat_d)}°{abs(lat_m):.4f}'{lat_dir}, "
                   f"{abs(lon_d)}°{abs(lon_m):.4f}'{lon_dir}")
        
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")
    
    @staticmethod
    def create_transform_from_bounds(min_lon: float, min_lat: float,
                                   max_lon: float, max_lat: float,
                                   width: int, height: int) -> tuple:
        """
        根据边界和尺寸创建仿射变换参数
        
        Args:
            min_lon, min_lat, max_lon, max_lat: 边界坐标
            width, height: 图像宽高
            
        Returns:
            仿射变换参数
        """
        pixel_width = (max_lon - min_lon) / width
        pixel_height = (max_lat - min_lat) / height
        
        return (min_lon, pixel_width, 0, max_lat, 0, -pixel_height)
