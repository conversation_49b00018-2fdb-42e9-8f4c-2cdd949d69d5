"""
数据验证模块
"""

import os
import numpy as np
from typing import Tuple, Optional, List, Dict, Any
import logging


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_file_path(file_path: str, check_exists: bool = True) -> bool:
        """
        验证文件路径
        
        Args:
            file_path: 文件路径
            check_exists: 是否检查文件存在
            
        Returns:
            是否有效
        """
        if not file_path or not isinstance(file_path, str):
            logging.error("文件路径不能为空")
            return False
        
        if check_exists and not os.path.exists(file_path):
            logging.error(f"文件不存在: {file_path}")
            return False
        
        # 检查文件扩展名
        valid_extensions = ['.tif', '.tiff', '.img', '.nc', '.hdf', '.h5']
        ext = os.path.splitext(file_path)[1].lower()
        if check_exists and ext not in valid_extensions:
            logging.warning(f"可能不支持的文件格式: {ext}")
        
        return True
    
    @staticmethod
    def validate_grid_level(level: int) -> bool:
        """
        验证网格级别
        
        Args:
            level: 网格级别
            
        Returns:
            是否有效
        """
        if not isinstance(level, int):
            logging.error("网格级别必须是整数")
            return False
        
        if level < 1 or level > 10:
            logging.error(f"网格级别必须在1-10之间，当前值: {level}")
            return False
        
        return True
    
    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """
        验证坐标
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            是否有效
        """
        if not isinstance(lat, (int, float)) or not isinstance(lon, (int, float)):
            logging.error("坐标必须是数值类型")
            return False
        
        if lat < -90 or lat > 90:
            logging.error(f"纬度必须在-90到90之间，当前值: {lat}")
            return False
        
        if lon < -180 or lon > 180:
            logging.error(f"经度必须在-180到180之间，当前值: {lon}")
            return False
        
        return True
    
    @staticmethod
    def validate_bounds(min_lon: float, min_lat: float, 
                       max_lon: float, max_lat: float) -> bool:
        """
        验证边界坐标
        
        Args:
            min_lon, min_lat, max_lon, max_lat: 边界坐标
            
        Returns:
            是否有效
        """
        # 验证各个坐标
        if not all(DataValidator.validate_coordinates(lat, lon) 
                  for lat, lon in [(min_lat, min_lon), (max_lat, max_lon)]):
            return False
        
        # 验证边界逻辑
        if min_lon >= max_lon:
            logging.error(f"最小经度({min_lon})必须小于最大经度({max_lon})")
            return False
        
        if min_lat >= max_lat:
            logging.error(f"最小纬度({min_lat})必须小于最大纬度({max_lat})")
            return False
        
        return True
    
    @staticmethod
    def validate_array(array: np.ndarray, min_shape: Tuple[int, int] = (1, 1)) -> bool:
        """
        验证数组
        
        Args:
            array: numpy数组
            min_shape: 最小形状
            
        Returns:
            是否有效
        """
        if not isinstance(array, np.ndarray):
            logging.error("输入必须是numpy数组")
            return False
        
        if array.ndim != 2:
            logging.error(f"数组必须是二维的，当前维度: {array.ndim}")
            return False
        
        if array.shape[0] < min_shape[0] or array.shape[1] < min_shape[1]:
            logging.error(f"数组形状{array.shape}小于最小要求{min_shape}")
            return False
        
        if array.size == 0:
            logging.error("数组不能为空")
            return False
        
        return True
    
    @staticmethod
    def validate_window_size(window_size: int, max_size: int = 100) -> bool:
        """
        验证窗口大小
        
        Args:
            window_size: 窗口大小
            max_size: 最大窗口大小
            
        Returns:
            是否有效
        """
        if not isinstance(window_size, int):
            logging.error("窗口大小必须是整数")
            return False
        
        if window_size < 1:
            logging.error(f"窗口大小必须大于0，当前值: {window_size}")
            return False
        
        if window_size > max_size:
            logging.error(f"窗口大小不能超过{max_size}，当前值: {window_size}")
            return False
        
        if window_size % 2 == 0:
            logging.warning(f"建议使用奇数窗口大小，当前值: {window_size}")
        
        return True
    
    @staticmethod
    def validate_aggregation_method(method: str) -> bool:
        """
        验证聚合方法
        
        Args:
            method: 聚合方法
            
        Returns:
            是否有效
        """
        from core.aggregation import AggregationMethod
        
        if not isinstance(method, str):
            logging.error("聚合方法必须是字符串")
            return False
        
        valid_methods = [m.value for m in AggregationMethod]
        if method not in valid_methods:
            logging.error(f"不支持的聚合方法: {method}，支持的方法: {valid_methods}")
            return False
        
        return True
    
    @staticmethod
    def validate_output_path(output_path: str, create_dir: bool = True) -> bool:
        """
        验证输出路径
        
        Args:
            output_path: 输出路径
            create_dir: 是否创建目录
            
        Returns:
            是否有效
        """
        if not output_path or not isinstance(output_path, str):
            logging.error("输出路径不能为空")
            return False
        
        # 检查目录
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            if create_dir:
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    logging.info(f"创建输出目录: {output_dir}")
                except Exception as e:
                    logging.error(f"无法创建输出目录 {output_dir}: {str(e)}")
                    return False
            else:
                logging.error(f"输出目录不存在: {output_dir}")
                return False
        
        # 检查文件权限
        if os.path.exists(output_path):
            if not os.access(output_path, os.W_OK):
                logging.error(f"没有写入权限: {output_path}")
                return False
        else:
            # 检查目录写入权限
            if output_dir and not os.access(output_dir, os.W_OK):
                logging.error(f"没有目录写入权限: {output_dir}")
                return False
        
        return True
    
    @staticmethod
    def validate_memory_usage(array_shape: Tuple[int, int], 
                            dtype: np.dtype = np.float64,
                            max_memory_gb: float = 4.0) -> bool:
        """
        验证内存使用量
        
        Args:
            array_shape: 数组形状
            dtype: 数据类型
            max_memory_gb: 最大内存使用量（GB）
            
        Returns:
            是否在限制内
        """
        # 计算内存使用量
        element_size = np.dtype(dtype).itemsize
        total_elements = np.prod(array_shape)
        memory_bytes = total_elements * element_size
        memory_gb = memory_bytes / (1024**3)
        
        if memory_gb > max_memory_gb:
            logging.warning(f"预计内存使用量 {memory_gb:.2f}GB 超过限制 {max_memory_gb}GB")
            return False
        
        logging.info(f"预计内存使用量: {memory_gb:.2f}GB")
        return True
    
    @staticmethod
    def validate_processing_parameters(params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证处理参数
        
        Args:
            params: 参数字典
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 必需参数
        required_params = ['input_file', 'output_file', 'grid_level']
        for param in required_params:
            if param not in params:
                errors.append(f"缺少必需参数: {param}")
        
        if errors:
            return False, errors
        
        # 验证各个参数
        if not DataValidator.validate_file_path(params['input_file']):
            errors.append("输入文件路径无效")
        
        if not DataValidator.validate_output_path(params['output_file']):
            errors.append("输出文件路径无效")
        
        if not DataValidator.validate_grid_level(params['grid_level']):
            errors.append("网格级别无效")
        
        # 可选参数验证
        if 'aggregation_method' in params:
            if not DataValidator.validate_aggregation_method(params['aggregation_method']):
                errors.append("聚合方法无效")
        
        if 'window_size' in params:
            if not DataValidator.validate_window_size(params['window_size']):
                errors.append("窗口大小无效")
        
        return len(errors) == 0, errors
