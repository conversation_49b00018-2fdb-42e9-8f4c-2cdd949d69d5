#!/usr/bin/env python3
"""
北斗网格转换工具 v2.0
主程序入口

使用方法:
1. GUI模式: python main.py
2. 命令行模式: python main.py --cli [参数]
3. 帮助信息: python main.py --help
"""

import sys
import os
import argparse
import logging
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
from core.aggregation import AggregationMethod


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.INFO if verbose else logging.WARNING
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def run_gui():
    """运行GUI界面"""
    try:
        from gui.main_window import MainWindow
        
        print("启动北斗网格转换工具 GUI...")
        app = MainWindow()
        app.run()
        
    except ImportError as e:
        print(f"GUI依赖缺失: {e}")
        print("请安装tkinter: pip install tkinter")
        sys.exit(1)
    except Exception as e:
        print(f"GUI启动失败: {e}")
        sys.exit(1)


def run_cli(args):
    """运行命令行模式"""
    try:
        # 创建配置
        config = ProcessingConfig(
            input_file=args.input,
            output_raster=args.output_raster,
            output_vector=args.output_vector,
            grid_level=args.grid_level,
            aggregation_method=args.aggregation_method,
            window_size=args.window_size,
            boundary_handling=args.boundary_handling,
            output_dtype=args.output_dtype,
            compression=args.compression,
            create_overview=args.create_overview,
            parallel_processing=args.parallel,
            max_memory_gb=args.max_memory,
            verbose=args.verbose
        )
        
        # 验证输入
        if not os.path.exists(config.input_file):
            print(f"错误: 输入文件不存在: {config.input_file}")
            sys.exit(1)
        
        if not (config.output_raster or config.output_vector):
            print("错误: 必须指定至少一种输出格式")
            sys.exit(1)
        
        # 创建处理器
        processor = BeidouGridProcessor(config)
        
        # 设置进度回调
        def progress_callback(percent, message):
            print(f"进度 {percent}%: {message}")
        
        processor.set_progress_callback(progress_callback)
        
        # 执行处理
        print("开始处理...")
        result = processor.process()
        
        if result['success']:
            print("处理完成!")
            print(f"输入形状: {result.get('input_shape')}")
            print(f"输出形状: {result.get('output_shape')}")
            print(f"网格级别: {result.get('grid_level')}")
            print(f"聚合方法: {result.get('aggregation_method')}")
            
            if 'output_files' in result:
                print("输出文件:")
                for file_type, file_path in result['output_files'].items():
                    print(f"  {file_type}: {file_path}")
            
            if 'statistics' in result:
                stats = result['statistics']
                print("统计信息:")
                print(f"  有效像素数: {stats.get('count')}")
                print(f"  最小值: {stats.get('min', 0):.4f}")
                print(f"  最大值: {stats.get('max', 0):.4f}")
                print(f"  平均值: {stats.get('mean', 0):.4f}")
                print(f"  标准差: {stats.get('std', 0):.4f}")
        else:
            print(f"处理失败: {result.get('error')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"处理失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="北斗网格转换工具 v2.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # GUI模式
  python main.py
  
  # 命令行模式 - 基本用法
  python main.py --cli -i input.tif -or output.tif -ov output.shp
  
  # 命令行模式 - 完整参数
  python main.py --cli -i input.tif -or output.tif -ov output.shp \\
    --grid-level 8 --aggregation-method mean --window-size 7 \\
    --output-dtype float32 --compression lzw --parallel --verbose
        """
    )
    
    # 模式选择
    parser.add_argument(
        '--cli', action='store_true',
        help='使用命令行模式（默认为GUI模式）'
    )
    
    # 输入输出参数
    parser.add_argument(
        '-i', '--input', type=str,
        help='输入栅格文件路径'
    )
    parser.add_argument(
        '-or', '--output-raster', type=str,
        help='输出栅格文件路径'
    )
    parser.add_argument(
        '-ov', '--output-vector', type=str,
        help='输出矢量文件路径'
    )
    
    # 网格参数
    parser.add_argument(
        '--grid-level', type=int, default=7, choices=range(1, 11),
        help='网格级别 (1-10，默认: 7)'
    )
    
    # 聚合参数
    aggregation_methods = [method.value for method in AggregationMethod]
    parser.add_argument(
        '--aggregation-method', type=str, default='max',
        choices=aggregation_methods,
        help=f'聚合方法 (默认: max)'
    )
    parser.add_argument(
        '--window-size', type=int, default=5,
        help='窗口大小 (默认: 5)'
    )
    parser.add_argument(
        '--boundary-handling', type=str, default='edge',
        choices=['edge', 'constant', 'reflect', 'wrap'],
        help='边界处理方式 (默认: edge)'
    )
    
    # 输出参数
    parser.add_argument(
        '--output-dtype', type=str, default='float32',
        choices=['float32', 'float64', 'int16', 'int32'],
        help='输出数据类型 (默认: float32)'
    )
    parser.add_argument(
        '--compression', type=str, default='lzw',
        choices=['none', 'lzw', 'deflate', 'packbits'],
        help='压缩方式 (默认: lzw)'
    )
    parser.add_argument(
        '--create-overview', action='store_true', default=True,
        help='创建概览图 (默认: True)'
    )
    parser.add_argument(
        '--no-overview', dest='create_overview', action='store_false',
        help='不创建概览图'
    )
    
    # 处理参数
    parser.add_argument(
        '--parallel', action='store_true',
        help='启用并行处理'
    )
    parser.add_argument(
        '--max-memory', type=float, default=4.0,
        help='最大内存使用量 (GB，默认: 4.0)'
    )
    parser.add_argument(
        '--chunk-size', type=int, default=1024,
        help='块大小 (默认: 1024)'
    )
    
    # 其他参数
    parser.add_argument(
        '-v', '--verbose', action='store_true',
        help='详细输出'
    )
    parser.add_argument(
        '--version', action='version', version='北斗网格转换工具 v2.0'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose if hasattr(args, 'verbose') else False)
    
    # 检查模式
    if args.cli:
        # 命令行模式
        if not args.input:
            print("错误: 命令行模式需要指定输入文件 (-i/--input)")
            parser.print_help()
            sys.exit(1)
        
        run_cli(args)
    else:
        # GUI模式
        run_gui()


if __name__ == '__main__':
    main()
