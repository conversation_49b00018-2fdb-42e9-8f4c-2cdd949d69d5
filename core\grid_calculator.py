"""
北斗网格计算模块
重构并优化的北斗网格编码算法
"""

import math
import numpy as np
from typing import Tuple, Dict, List, Optional
from dataclasses import dataclass


@dataclass
class GridLevel:
    """网格级别配置"""
    level: int
    lon_size: float  # 经度尺寸（度）
    lat_size: float  # 纬度尺寸（度）
    description: str


class BeidouGridCalculator:
    """北斗网格计算器"""
    
    # 网格级别配置表
    GRID_LEVELS = {
        1: GridLevel(1, 6.0, 4.0, "一级网格：6°×4°"),
        2: GridLevel(2, 30.0/60, 30.0/60, "二级网格：30'×30'"),
        3: <PERSON><PERSON><PERSON><PERSON><PERSON>(3, 15.0/60, 10.0/60, "三级网格：15'×10'"),
        4: <PERSON><PERSON><PERSON><PERSON>l(4, 1.0/60, 1.0/60, "四级网格：1'×1'"),
        5: <PERSON><PERSON><PERSON><PERSON><PERSON>(5, 4.0/3600, 4.0/3600, "五级网格：4\"×4\""),
        6: G<PERSON><PERSON><PERSON>l(6, 2.0/3600, 2.0/3600, "六级网格：2\"×2\""),
        7: <PERSON>ridLevel(7, 1.0/14400, 1.0/14400, "七级网格：1/4\"×1/4\""),
        8: GridLevel(8, 1.0/115200, 1.0/115200, "八级网格：1/32\"×1/32\""),
        9: GridLevel(9, 1.0/921600, 1.0/921600, "九级网格：1/256\"×1/256\""),
        10: GridLevel(10, 1.0/7372800, 1.0/7372800, "十级网格：1/2048\"×1/2048\"")
    }
    
    def __init__(self):
        """初始化网格计算器"""
        self._cache = {}  # 编码缓存
    
    def get_grid_size(self, level: int) -> Tuple[float, float]:
        """
        获取指定级别的网格尺寸
        
        Args:
            level: 网格级别 (1-10)
            
        Returns:
            (lon_size, lat_size): 经纬度尺寸
        """
        if level not in self.GRID_LEVELS:
            raise ValueError(f"不支持的网格级别: {level}，支持范围: 1-10")
        
        grid_level = self.GRID_LEVELS[level]
        return grid_level.lon_size, grid_level.lat_size
    
    def calculate_grid_dimensions(self, min_lon: float, min_lat: float, 
                                max_lon: float, max_lat: float, 
                                level: int) -> Tuple[int, int]:
        """
        计算网格维度
        
        Args:
            min_lon, min_lat: 最小经纬度
            max_lon, max_lat: 最大经纬度
            level: 网格级别
            
        Returns:
            (rows, cols): 网格行列数
        """
        lon_size, lat_size = self.get_grid_size(level)
        
        cols = int(math.ceil((max_lon - min_lon) / lon_size))
        rows = int(math.ceil((max_lat - min_lat) / lat_size))
        
        return rows, cols
    
    def calculate_grid_code(self, lat: float, lon: float, level: int) -> str:
        """
        计算经纬度对应的北斗网格编码
        
        Args:
            lat: 纬度
            lon: 经度
            level: 网格级别 (1-10)
            
        Returns:
            网格编码字符串
        """
        if level < 1 or level > 10:
            raise ValueError(f"网格级别必须在1-10之间，当前值: {level}")
        
        # 检查缓存
        cache_key = (lat, lon, level)
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # 逐级计算编码
        codes = {}
        
        # 一级网格
        codes[1] = self._calculate_first_level(lat, lon)
        
        if level >= 2:
            codes[2] = self._calculate_second_level(lat, lon, codes[1])
        
        if level >= 3:
            codes[3] = self._calculate_third_level(lat, lon, codes[2])
        
        if level >= 4:
            codes[4] = self._calculate_fourth_level(lat, lon, codes[3])
        
        if level >= 5:
            codes[5] = self._calculate_fifth_level(lat, lon, codes[4])
        
        if level >= 6:
            codes[6] = self._calculate_sixth_level(lat, lon, codes[5])
        
        if level >= 7:
            codes[7] = self._calculate_seventh_level(lat, lon, codes[6])
        
        if level >= 8:
            codes[8] = self._calculate_eighth_level(lat, lon, codes[7])
        
        if level >= 9:
            codes[9] = self._calculate_ninth_level(lat, lon, codes[8])
        
        if level >= 10:
            codes[10] = self._calculate_tenth_level(lat, lon, codes[9])
        
        result = codes[level]
        self._cache[cache_key] = result
        return result
    
    def _calculate_first_level(self, lat: float, lon: float) -> str:
        """计算一级网格编码：6°×4°"""
        code1 = 'N' if lat >= 0 else 'S'
        code23 = str(int((lon + 180) / 6) + 1).zfill(2)
        code4 = chr(ord('A') + int(abs(lat) / 4))
        return code1 + code23 + code4
    
    def _calculate_second_level(self, lat: float, lon: float, first_code: str) -> str:
        """计算二级网格编码：30'×30'"""
        lon_base = int(lon / 6) * 6
        lat_base = int(abs(lat) / 4) * 4
        
        a2 = int((lon - lon_base) * 2) + 1
        b2 = int((abs(lat) - lat_base) * 2) + 1
        
        code5 = a2 - 1
        code6 = b2 - 1
        
        # 处理特殊情况
        if code6 >= 10:
            code6_dict = {10: 'A', 11: 'B'}
            code6 = code6_dict.get(code6, str(code6))
        
        return first_code + str(code5) + str(code6)
    
    def _calculate_third_level(self, lat: float, lon: float, second_code: str) -> str:
        """计算三级网格编码：15'×10'"""
        # 获取二级网格基准点
        lon_base, lat_base = self._get_second_level_base(lat, lon)
        
        # Z字形编码数组
        array3 = [[0, 2, 4], [1, 3, 5]]
        
        lon_index = 1 if (lon - lon_base) * 4 >= 1 else 0
        lat_diff = (abs(lat) - lat_base) * 6
        
        if 0 < lat_diff <= 1:
            lat_index = 0
        elif 1 < lat_diff <= 2:
            lat_index = 1
        else:  # 2 < lat_diff <= 3
            lat_index = 2
        
        code7 = array3[lon_index][lat_index]
        return second_code + str(code7)
    
    def _get_second_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取二级网格基准点"""
        lon_base1 = int(lon / 6) * 6
        lat_base1 = int(abs(lat) / 4) * 4
        
        a2 = int((lon - lon_base1) * 2) + 1
        b2 = int((abs(lat) - lat_base1) * 2) + 1
        
        lon_base2 = lon_base1 + (a2 - 1) * 0.5
        lat_base2 = lat_base1 + (b2 - 1) * 0.5
        
        return lon_base2, lat_base2

    def _calculate_fourth_level(self, lat: float, lon: float, third_code: str) -> str:
        """计算四级网格编码：1'×1'"""
        lon_base, lat_base = self._get_third_level_base(lat, lon)

        a4 = int((lon - lon_base) * 60) + 1
        b4 = int((abs(lat) - lat_base) * 60) + 1

        code8 = a4 - 1
        code9 = b4 - 1

        # 处理大于9的情况
        code8_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
        if code8 >= 10:
            code8 = code8_dict.get(code8, str(code8))

        return third_code + str(code8) + str(code9)

    def _calculate_fifth_level(self, lat: float, lon: float, fourth_code: str) -> str:
        """计算五级网格编码：4"×4" """
        lon_base, lat_base = self._get_fourth_level_base(lat, lon)

        a5 = int((lon - lon_base) * 900) + 1
        b5 = int((abs(lat) - lat_base) * 900) + 1

        code10 = a5 - 1
        code11 = b5 - 1

        # 处理大于9的情况
        code_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
        if code10 >= 10:
            code10 = code_dict.get(code10, str(code10))
        if code11 >= 10:
            code11 = code_dict.get(code11, str(code11))

        return fourth_code + str(code10) + str(code11)

    def _calculate_sixth_level(self, lat: float, lon: float, fifth_code: str) -> str:
        """计算六级网格编码：2"×2" """
        lon_base, lat_base = self._get_fifth_level_base(lat, lon)

        # Z字形编码数组
        array6 = [[0, 2], [1, 3]]

        lon_index = 1 if (lon - lon_base) * 1800 >= 1 else 0
        lat_index = 1 if (abs(lat) - lat_base) * 1800 >= 1 else 0

        code12 = array6[lon_index][lat_index]
        return fifth_code + str(code12)

    def _calculate_seventh_level(self, lat: float, lon: float, sixth_code: str) -> str:
        """计算七级网格编码：1/4"×1/4" """
        lon_base, lat_base = self._get_sixth_level_base(lat, lon)

        a7 = int((lon - lon_base) * 14400) + 1
        b7 = int((abs(lat) - lat_base) * 14400) + 1

        code13 = a7 - 1
        code14 = b7 - 1

        return sixth_code + str(code13) + str(code14)

    def _calculate_eighth_level(self, lat: float, lon: float, seventh_code: str) -> str:
        """计算八级网格编码：1/32"×1/32" """
        lon_base, lat_base = self._get_seventh_level_base(lat, lon)

        a8 = int((lon - lon_base) * 115200) + 1
        b8 = int((abs(lat) - lat_base) * 115200) + 1

        code15 = a8 - 1
        code16 = b8 - 1

        return seventh_code + str(code15) + str(code16)

    def _calculate_ninth_level(self, lat: float, lon: float, eighth_code: str) -> str:
        """计算九级网格编码：1/256"×1/256" """
        lon_base, lat_base = self._get_eighth_level_base(lat, lon)

        a9 = int((lon - lon_base) * 921600) + 1
        b9 = int((abs(lat) - lat_base) * 921600) + 1

        code17 = a9 - 1
        code18 = b9 - 1

        return eighth_code + str(code17) + str(code18)

    def _calculate_tenth_level(self, lat: float, lon: float, ninth_code: str) -> str:
        """计算十级网格编码：1/2048"×1/2048" """
        lon_base, lat_base = self._get_ninth_level_base(lat, lon)

        a10 = int((lon - lon_base) * 7372800) + 1
        b10 = int((abs(lat) - lat_base) * 7372800) + 1

        code19 = a10 - 1
        code20 = b10 - 1

        return ninth_code + str(code19) + str(code20)

    def _get_third_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取三级网格基准点"""
        lon_base2, lat_base2 = self._get_second_level_base(lat, lon)

        a3 = int((lon - lon_base2) * 4) + 1
        b3 = int((abs(lat) - lat_base2) * 6) + 1

        lon_base3 = lon_base2 + (a3 - 1) * 0.25
        lat_base3 = lat_base2 + (b3 - 1) * (1.0 / 6)

        return lon_base3, lat_base3

    def _get_fourth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取四级网格基准点"""
        lon_base3, lat_base3 = self._get_third_level_base(lat, lon)

        a4 = int((lon - lon_base3) * 60) + 1
        b4 = int((abs(lat) - lat_base3) * 60) + 1

        lon_base4 = lon_base3 + (a4 - 1) * (1.0 / 60)
        lat_base4 = lat_base3 + (b4 - 1) * (1.0 / 60)

        return lon_base4, lat_base4

    def _get_fifth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取五级网格基准点"""
        lon_base4, lat_base4 = self._get_fourth_level_base(lat, lon)

        a5 = int((lon - lon_base4) * 900) + 1
        b5 = int((abs(lat) - lat_base4) * 900) + 1

        lon_base5 = lon_base4 + (a5 - 1) * (1.0 / 900)
        lat_base5 = lat_base4 + (b5 - 1) * (1.0 / 900)

        return lon_base5, lat_base5

    def _get_sixth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取六级网格基准点"""
        lon_base5, lat_base5 = self._get_fifth_level_base(lat, lon)

        a6 = int((lon - lon_base5) * 1800) + 1
        b6 = int((abs(lat) - lat_base5) * 1800) + 1

        lon_base6 = lon_base5 + (a6 - 1) * (1.0 / 1800)
        lat_base6 = lat_base5 + (b6 - 1) * (1.0 / 1800)

        return lon_base6, lat_base6

    def _get_seventh_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取七级网格基准点"""
        lon_base6, lat_base6 = self._get_sixth_level_base(lat, lon)

        a7 = int((lon - lon_base6) * 14400) + 1
        b7 = int((abs(lat) - lat_base6) * 14400) + 1

        lon_base7 = lon_base6 + (a7 - 1) * (1.0 / 14400)
        lat_base7 = lat_base6 + (b7 - 1) * (1.0 / 14400)

        return lon_base7, lat_base7

    def _get_eighth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取八级网格基准点"""
        lon_base7, lat_base7 = self._get_seventh_level_base(lat, lon)

        a8 = int((lon - lon_base7) * 115200) + 1
        b8 = int((abs(lat) - lat_base7) * 115200) + 1

        lon_base8 = lon_base7 + (a8 - 1) * (1.0 / 115200)
        lat_base8 = lat_base7 + (b8 - 1) * (1.0 / 115200)

        return lon_base8, lat_base8

    def _get_ninth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
        """获取九级网格基准点"""
        lon_base8, lat_base8 = self._get_eighth_level_base(lat, lon)

        a9 = int((lon - lon_base8) * 921600) + 1
        b9 = int((abs(lat) - lat_base8) * 921600) + 1

        lon_base9 = lon_base8 + (a9 - 1) * (1.0 / 921600)
        lat_base9 = lat_base8 + (b9 - 1) * (1.0 / 921600)

        return lon_base9, lat_base9

    def clear_cache(self):
        """清空编码缓存"""
        self._cache.clear()

    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)

    def batch_calculate_codes(self, coordinates: List[Tuple[float, float]],
                            level: int) -> List[str]:
        """
        批量计算网格编码

        Args:
            coordinates: 坐标列表 [(lat, lon), ...]
            level: 网格级别

        Returns:
            编码列表
        """
        return [self.calculate_grid_code(lat, lon, level)
                for lat, lon in coordinates]
