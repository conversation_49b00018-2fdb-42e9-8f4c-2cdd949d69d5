使用说明
一、北斗网格转换工具
1、需求说明
存在栅格数据文件（tif格式）需要按照北斗网格等级（1-10）划分生成对应矢量（shp文件）
2、实现方法
1、读取tif栅格数据，将tif栅格数据转换为numpy数组；
2、遍历numpy数组，根据选定北斗网格等级，挑选局部最大值并赋值给当前行列；
3、返回局部最大值数组，转换为当前北斗网格等级tif栅格数据；
4、将北斗网格等级tif栅格数据由双精度栅格转为整型栅格；
5、最后把北斗网格等级tif整型栅格转换为矢量shape。
3、工具使用说明
（1）打开arcgis连接到工具存放文件夹，打开工具，如下图所示： 
 
（2）按下图所示输入对应参数：
 
参数①(tif文件路径)：存放待处理的栅格数据（tif文件）路径；如：“E:\WORK\tiff\test.tif”
参数②(tif转换后文件名称)：输出转换后的tif文件名称；如：“new.tif”
参数③(划分北斗网格等级，1-10)：预期输出的北斗网格等级；如：“7”
注意：
①tif文件存放路径最好是全英文路径，中文可能会读取不到文件出现错误。
②因需存储数组值比对，故若tif文件较大，则需电脑内存满足，否则将出现无法继续读取报错。
③当划分北斗网格等级越高，则需要对比数组值数量越多，可能会运行较长时间。
（3）等待脚本运行结束，生成结果。
 
（4）输出的北斗网格转换后tif文件存放路径与输入的tif文件相同，打开结果如下图所示：
 
 
（5）原始tif文件经运行工具后将输出三个结果：
 
（a）北斗网格转换后双精度栅格tif文件，如：“new.tif”
 
（b）北斗网格转换后整型栅格tif文件，如：“int_new.tif”
 
（c）北斗网格转换整型tif栅格转换后矢量shape，如：“int_new.shp”
 
