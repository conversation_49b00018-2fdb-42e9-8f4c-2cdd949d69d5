"""
数据聚合模块
提供多种数据聚合方法
"""

import numpy as np
from enum import Enum
from typing import Union, Callable, Optional
from scipy import stats


class AggregationMethod(Enum):
    """聚合方法枚举"""
    MAX = "max"
    MIN = "min"
    MEAN = "mean"
    MEDIAN = "median"
    MODE = "mode"
    STD = "std"
    VAR = "var"
    SUM = "sum"
    COUNT = "count"
    PERCENTILE_25 = "p25"
    PERCENTILE_75 = "p75"
    PERCENTILE_90 = "p90"
    PERCENTILE_95 = "p95"
    RANGE = "range"
    IQR = "iqr"  # 四分位距


class DataAggregator:
    """数据聚合器"""
    
    def __init__(self, method: Union[AggregationMethod, str] = AggregationMethod.MAX,
                 window_size: int = 5, boundary_handling: str = "edge"):
        """
        初始化聚合器
        
        Args:
            method: 聚合方法
            window_size: 窗口大小
            boundary_handling: 边界处理方式 ("edge", "constant", "reflect", "wrap")
        """
        if isinstance(method, str):
            method = AggregationMethod(method)
        
        self.method = method
        self.window_size = window_size
        self.boundary_handling = boundary_handling
        
        # 聚合函数映射
        self._aggregation_functions = {
            AggregationMethod.MAX: self._max_aggregation,
            AggregationMethod.MIN: self._min_aggregation,
            AggregationMethod.MEAN: self._mean_aggregation,
            AggregationMethod.MEDIAN: self._median_aggregation,
            AggregationMethod.MODE: self._mode_aggregation,
            AggregationMethod.STD: self._std_aggregation,
            AggregationMethod.VAR: self._var_aggregation,
            AggregationMethod.SUM: self._sum_aggregation,
            AggregationMethod.COUNT: self._count_aggregation,
            AggregationMethod.PERCENTILE_25: lambda x: self._percentile_aggregation(x, 25),
            AggregationMethod.PERCENTILE_75: lambda x: self._percentile_aggregation(x, 75),
            AggregationMethod.PERCENTILE_90: lambda x: self._percentile_aggregation(x, 90),
            AggregationMethod.PERCENTILE_95: lambda x: self._percentile_aggregation(x, 95),
            AggregationMethod.RANGE: self._range_aggregation,
            AggregationMethod.IQR: self._iqr_aggregation,
        }
    
    def aggregate(self, array: np.ndarray, nodata_value: Optional[float] = None, progress_callback=None, cancel_check=None) -> np.ndarray:
        """
        对数组进行聚合处理
        
        Args:
            array: 输入数组
            nodata_value: 无效值
            
        Returns:
            聚合后的数组
        """
        if array.ndim != 2:
            raise ValueError("输入数组必须是二维数组")
        
        rows, cols = array.shape
        
        # 处理边界
        pad_width = self.window_size // 2
        if self.boundary_handling == "constant":
            padded_array = np.pad(array, pad_width=pad_width, 
                                mode='constant', constant_values=nodata_value or 0)
        else:
            padded_array = np.pad(array, pad_width=pad_width, mode=self.boundary_handling)
        
        result = np.zeros_like(array, dtype=float)
        aggregation_func = self._aggregation_functions[self.method]
        
        # 滑动窗口处理
        total_pixels = rows * cols
        processed_pixels = 0

        for i in range(rows):
            for j in range(cols):
                # 提取窗口
                window = padded_array[i:i + self.window_size, j:j + self.window_size]

                # 处理无效值
                if nodata_value is not None:
                    window = window[window != nodata_value]
                    if window.size == 0:
                        result[i, j] = nodata_value
                        processed_pixels += 1
                        continue

                # 应用聚合函数
                result[i, j] = aggregation_func(window)
                processed_pixels += 1

                # 检查是否取消（每处理1000个像素检查一次）
                if processed_pixels % 1000 == 0:
                    if cancel_check:
                        cancel_check()

                    # 更新进度
                    if progress_callback:
                        progress_percent = int((processed_pixels / total_pixels) * 100)
                        detail = f"已处理 {processed_pixels:,} / {total_pixels:,} 像素 ({progress_percent}%)"
                        # 传递像素处理的详细信息用于时间估算
                        stage_progress = 40 + int(progress_percent * 0.2)
                        progress_callback(stage_progress, "执行数据聚合", detail, {
                            'pixels_processed': processed_pixels,
                            'total_pixels': total_pixels,
                            'stage_percent': progress_percent
                        })
        
        return result
    
    def aggregate_blocks(self, array: np.ndarray, block_shape: tuple,
                        nodata_value: Optional[float] = None) -> np.ndarray:
        """
        按块进行聚合
        
        Args:
            array: 输入数组
            block_shape: 块的形状 (block_rows, block_cols)
            nodata_value: 无效值
            
        Returns:
            聚合后的数组
        """
        rows, cols = array.shape
        block_rows, block_cols = block_shape
        
        # 计算输出数组大小
        out_rows = int(np.ceil(rows / block_rows))
        out_cols = int(np.ceil(cols / block_cols))
        
        result = np.zeros((out_rows, out_cols), dtype=float)
        aggregation_func = self._aggregation_functions[self.method]
        
        for i in range(out_rows):
            for j in range(out_cols):
                # 计算块的边界
                start_row = i * block_rows
                end_row = min((i + 1) * block_rows, rows)
                start_col = j * block_cols
                end_col = min((j + 1) * block_cols, cols)
                
                # 提取块
                block = array[start_row:end_row, start_col:end_col]
                
                # 处理无效值
                if nodata_value is not None:
                    valid_data = block[block != nodata_value]
                    if valid_data.size == 0:
                        result[i, j] = nodata_value
                        continue
                    block = valid_data
                
                # 应用聚合函数
                result[i, j] = aggregation_func(block)
        
        return result
    
    # 聚合函数实现
    def _max_aggregation(self, data: np.ndarray) -> float:
        """最大值聚合"""
        return np.max(data) if data.size > 0 else 0.0
    
    def _min_aggregation(self, data: np.ndarray) -> float:
        """最小值聚合"""
        return np.min(data) if data.size > 0 else 0.0
    
    def _mean_aggregation(self, data: np.ndarray) -> float:
        """平均值聚合"""
        return np.mean(data) if data.size > 0 else 0.0
    
    def _median_aggregation(self, data: np.ndarray) -> float:
        """中位数聚合"""
        return np.median(data) if data.size > 0 else 0.0
    
    def _mode_aggregation(self, data: np.ndarray) -> float:
        """众数聚合"""
        if data.size == 0:
            return 0.0
        try:
            mode_result = stats.mode(data, keepdims=True)
            return float(mode_result.mode[0])
        except:
            return float(data[0])  # 如果无法计算众数，返回第一个值
    
    def _std_aggregation(self, data: np.ndarray) -> float:
        """标准差聚合"""
        return np.std(data) if data.size > 0 else 0.0
    
    def _var_aggregation(self, data: np.ndarray) -> float:
        """方差聚合"""
        return np.var(data) if data.size > 0 else 0.0
    
    def _sum_aggregation(self, data: np.ndarray) -> float:
        """求和聚合"""
        return np.sum(data) if data.size > 0 else 0.0
    
    def _count_aggregation(self, data: np.ndarray) -> float:
        """计数聚合"""
        return float(data.size)
    
    def _percentile_aggregation(self, data: np.ndarray, percentile: float) -> float:
        """百分位数聚合"""
        return np.percentile(data, percentile) if data.size > 0 else 0.0
    
    def _range_aggregation(self, data: np.ndarray) -> float:
        """极差聚合"""
        return (np.max(data) - np.min(data)) if data.size > 0 else 0.0
    
    def _iqr_aggregation(self, data: np.ndarray) -> float:
        """四分位距聚合"""
        if data.size == 0:
            return 0.0
        q75 = np.percentile(data, 75)
        q25 = np.percentile(data, 25)
        return q75 - q25
    
    @staticmethod
    def get_available_methods() -> list:
        """获取所有可用的聚合方法"""
        return [method.value for method in AggregationMethod]
    
    @staticmethod
    def get_method_description(method: Union[AggregationMethod, str]) -> str:
        """获取聚合方法的描述"""
        if isinstance(method, str):
            method = AggregationMethod(method)
        
        descriptions = {
            AggregationMethod.MAX: "最大值",
            AggregationMethod.MIN: "最小值",
            AggregationMethod.MEAN: "平均值",
            AggregationMethod.MEDIAN: "中位数",
            AggregationMethod.MODE: "众数",
            AggregationMethod.STD: "标准差",
            AggregationMethod.VAR: "方差",
            AggregationMethod.SUM: "求和",
            AggregationMethod.COUNT: "计数",
            AggregationMethod.PERCENTILE_25: "25%分位数",
            AggregationMethod.PERCENTILE_75: "75%分位数",
            AggregationMethod.PERCENTILE_90: "90%分位数",
            AggregationMethod.PERCENTILE_95: "95%分位数",
            AggregationMethod.RANGE: "极差",
            AggregationMethod.IQR: "四分位距",
        }
        
        return descriptions.get(method, "未知方法")
