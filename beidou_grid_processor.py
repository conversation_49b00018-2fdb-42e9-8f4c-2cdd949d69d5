"""
北斗网格转换处理器
整合所有功能的主处理类
"""

import os
import numpy as np
import logging
from typing import Optional, Dict, Any, Tuple, Callable
from dataclasses import dataclass

from core import BeidouGridCalculator, AggregationMethod, DataAggregator
from data.raster_io import <PERSON><PERSON><PERSON><PERSON><PERSON>, RasterWriter
from data.vector_io import VectorWriter
from data.data_validator import DataValidator


@dataclass
class ProcessingConfig:
    """处理配置"""
    # 输入输出
    input_file: str
    output_raster: Optional[str] = None
    output_vector: Optional[str] = None
    output_integer_raster: bool = True  # 是否同时输出整型栅格文件
    
    # 网格参数
    grid_level: int = 7
    
    # 聚合参数
    aggregation_method: str = "max"
    window_size: int = 5
    boundary_handling: str = "edge"
    
    # 输出参数
    output_dtype: str = "float32"
    compression: str = "lzw"
    create_overview: bool = True
    
    # 处理参数
    chunk_size: int = 1024
    parallel_processing: bool = False
    max_memory_gb: float = 4.0
    
    # 其他选项
    nodata_value: Optional[float] = None
    preserve_crs: bool = True
    verbose: bool = True


class BeidouGridProcessor:
    """北斗网格转换处理器"""
    
    def __init__(self, config: ProcessingConfig):
        """
        初始化处理器
        
        Args:
            config: 处理配置
        """
        self.config = config
        self.grid_calculator = BeidouGridCalculator()
        self.aggregator = None
        self.progress_callback: Optional[Callable] = None
        self.is_cancelled = False
        self.temp_files = []  # 记录临时文件
        
        # 设置日志
        if config.verbose:
            logging.basicConfig(level=logging.INFO)
        
        # 验证配置
        self._validate_config()
    
    def set_progress_callback(self, callback: Callable[[int, str, str, dict], None]):
        """
        设置进度回调函数

        Args:
            callback: 回调函数，参数为 (progress_percent, message, detail, extra_info)
        """
        self.progress_callback = callback

    def _update_progress(self, percent: int, message: str, detail: str = "", extra_info: dict = None):
        """更新进度"""
        if self.progress_callback:
            if extra_info:
                self.progress_callback(percent, message, detail, extra_info)
            else:
                # 为了向后兼容，如果没有额外信息，仍然使用3参数调用
                try:
                    self.progress_callback(percent, message, detail, {})
                except TypeError:
                    # 如果回调函数只接受3个参数，则使用旧方式
                    self.progress_callback(percent, message, detail)
        elif self.config.verbose:
            logging.info(f"进度 {percent}%: {message} {detail}")

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        self._cleanup_temp_files()
        logging.info("处理已被用户取消")

    def _check_cancelled(self):
        """检查是否已取消"""
        if self.is_cancelled:
            raise InterruptedError("处理已被用户取消")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        import os
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logging.info(f"已删除临时文件: {temp_file}")
            except Exception as e:
                logging.warning(f"删除临时文件失败 {temp_file}: {e}")
        self.temp_files.clear()

    def _get_file_size(self, file_path: str) -> str:
        """获取文件大小的可读格式"""
        import os
        try:
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                if size_bytes < 1024:
                    return f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    return f"{size_bytes / 1024:.1f} KB"
                elif size_bytes < 1024 * 1024 * 1024:
                    return f"{size_bytes / (1024 * 1024):.1f} MB"
                else:
                    return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
            return "未知大小"
        except Exception:
            return "未知大小"
    
    def _validate_config(self):
        """验证配置"""
        params = {
            'input_file': self.config.input_file,
            'output_file': self.config.output_raster or self.config.output_vector or 'temp.tif',
            'grid_level': self.config.grid_level,
            'aggregation_method': self.config.aggregation_method,
            'window_size': self.config.window_size
        }
        
        is_valid, errors = DataValidator.validate_processing_parameters(params)
        if not is_valid:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    def process(self) -> Dict[str, Any]:
        """
        执行完整的处理流程
        
        Returns:
            处理结果信息
        """
        try:
            self._update_progress(0, "开始处理", f"输入文件: {self.config.input_file}")
            self._check_cancelled()

            # 1. 读取输入数据
            self._update_progress(10, "读取输入数据", "正在加载栅格文件...")
            input_data = self._read_input_data()
            self._check_cancelled()

            # 2. 数据预处理
            self._update_progress(20, "数据预处理", f"数据尺寸: {input_data['array'].shape}")
            processed_data = self._preprocess_data(input_data)
            self._check_cancelled()

            # 3. 聚合处理
            self._update_progress(40, "执行数据聚合", f"聚合方法: {self.config.aggregation_method}, 窗口大小: {self.config.window_size}")
            aggregated_data = self._aggregate_data(processed_data)
            self._check_cancelled()

            # 4. 生成网格
            self._update_progress(60, "生成北斗网格", f"网格级别: {self.config.grid_level}")
            grid_data = self._generate_grid(aggregated_data)
            self._check_cancelled()

            # 5. 输出结果
            self._update_progress(80, "输出结果", "正在写入输出文件...")
            output_info = self._write_outputs(grid_data)
            self._check_cancelled()

            self._update_progress(100, "处理完成", f"输出网格数量: {np.count_nonzero(~np.isnan(grid_data))}")

            # 处理完成后稍等一下让用户看到完成信息
            import time
            time.sleep(1.5)

            return {
                'success': True,
                'input_shape': input_data['array'].shape,
                'output_shape': grid_data.shape,
                'grid_level': self.config.grid_level,
                'aggregation_method': self.config.aggregation_method,
                'output_files': output_info,
                'statistics': self._calculate_statistics(grid_data)
            }
            
        except Exception as e:
            logging.error(f"处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _read_input_data(self) -> Dict[str, Any]:
        """读取输入数据"""
        with RasterReader(self.config.input_file) as reader:
            array = reader.read_array(masked=False)
            bounds = reader.get_bounds()
            transform = reader.get_transform()
            crs = reader.get_crs()
            nodata = reader.get_nodata_value()
            
            # 处理掩码数组
            if hasattr(array, 'mask'):
                array = array.filled(nodata or np.nan)
            
            return {
                'array': array,
                'bounds': bounds,
                'transform': transform,
                'crs': crs,
                'nodata': nodata or self.config.nodata_value
            }
    
    def _preprocess_data(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """数据预处理"""
        array = input_data['array']
        nodata = input_data['nodata']
        
        # 验证数组
        if not DataValidator.validate_array(array):
            raise ValueError("输入数组验证失败")
        
        # 验证内存使用
        if not DataValidator.validate_memory_usage(array.shape, array.dtype, self.config.max_memory_gb):
            logging.warning("内存使用量可能过大，建议分块处理")
        
        # 处理无效值
        if nodata is not None:
            array = np.where(array == nodata, np.nan, array)
        
        input_data['array'] = array
        return input_data
    
    def _aggregate_data(self, input_data: Dict[str, Any]) -> np.ndarray:
        """数据聚合"""
        array = input_data['array']
        
        # 创建聚合器
        self.aggregator = DataAggregator(
            method=self.config.aggregation_method,
            window_size=self.config.window_size,
            boundary_handling=self.config.boundary_handling
        )
        
        # 执行聚合
        if self.config.parallel_processing:
            # TODO: 实现并行处理
            aggregated = self.aggregator.aggregate(array, self.config.nodata_value, self._update_progress, self._check_cancelled)
        else:
            aggregated = self.aggregator.aggregate(array, self.config.nodata_value, self._update_progress, self._check_cancelled)
        
        return aggregated
    
    def _generate_grid(self, aggregated_data: np.ndarray) -> np.ndarray:
        """生成北斗网格"""
        # 获取边界信息
        bounds = self._get_processing_bounds()
        min_lon, min_lat, max_lon, max_lat = bounds

        self._update_progress(62, "计算网格维度", f"边界范围: ({min_lon:.4f}, {min_lat:.4f}) - ({max_lon:.4f}, {max_lat:.4f})")

        # 计算网格维度
        rows, cols = self.grid_calculator.calculate_grid_dimensions(
            min_lon, min_lat, max_lon, max_lat, self.config.grid_level
        )

        self._update_progress(65, "重采样数据", f"目标网格尺寸: {rows} x {cols} = {rows*cols:,} 个网格")

        # 生成网格数据
        grid_data = self._resample_to_grid(aggregated_data, (rows, cols))

        return grid_data
    
    def _resample_to_grid(self, data: np.ndarray, target_shape: Tuple[int, int]) -> np.ndarray:
        """重采样到目标网格"""
        from scipy.ndimage import zoom
        
        # 计算缩放因子
        zoom_factors = (target_shape[0] / data.shape[0], target_shape[1] / data.shape[1])
        
        # 重采样
        if self.config.aggregation_method == "max":
            # 对于最大值，使用最近邻插值
            resampled = zoom(data, zoom_factors, order=0)
        else:
            # 对于其他方法，使用双线性插值
            resampled = zoom(data, zoom_factors, order=1)
        
        return resampled
    
    def _write_outputs(self, grid_data: np.ndarray) -> Dict[str, str]:
        """写入输出文件"""
        output_files = {}
        bounds = self._get_processing_bounds()
        
        # 写入栅格文件
        if self.config.output_raster:
            self._update_progress(82, "写入栅格文件", f"输出文件: {self.config.output_raster}")
            # 记录输出文件以便取消时清理
            self.temp_files.append(self.config.output_raster)
            success = self._write_raster_output(grid_data, bounds)
            if success:
                output_files['raster'] = self.config.output_raster
                # 成功写入后从临时文件列表中移除
                if self.config.output_raster in self.temp_files:
                    self.temp_files.remove(self.config.output_raster)
                self._update_progress(85, "浮点栅格文件写入完成", f"文件大小: {self._get_file_size(self.config.output_raster)}")

                # 如果需要，同时生成整型栅格文件
                if self.config.output_integer_raster:
                    int_raster_path = self._get_integer_raster_path(self.config.output_raster)
                    self._update_progress(86, "写入整型栅格文件", f"输出文件: {int_raster_path}")
                    self.temp_files.append(int_raster_path)
                    int_success = self._write_integer_raster_output(grid_data, bounds, int_raster_path)
                    if int_success:
                        output_files['integer_raster'] = int_raster_path
                        if int_raster_path in self.temp_files:
                            self.temp_files.remove(int_raster_path)
                        self._update_progress(87, "整型栅格文件写入完成", f"文件大小: {self._get_file_size(int_raster_path)}")

        # 写入矢量文件
        if self.config.output_vector:
            self._update_progress(90, "写入矢量文件", f"输出文件: {self.config.output_vector}")
            # 记录输出文件以便取消时清理
            self.temp_files.append(self.config.output_vector)

            # 如果有整型栅格文件，优先使用整型数据生成矢量（更高效且符合原始ArcPy逻辑）
            vector_data = grid_data
            if self.config.output_integer_raster and 'integer_raster' in output_files:
                # 使用整型数据
                vector_data = np.where(np.isnan(grid_data), 0, np.floor(grid_data)).astype(np.int32)

            success = self._write_vector_output(vector_data, bounds)
            if success:
                output_files['vector'] = self.config.output_vector
                # 成功写入后从临时文件列表中移除
                if self.config.output_vector in self.temp_files:
                    self.temp_files.remove(self.config.output_vector)
                self._update_progress(95, "矢量文件写入完成", f"文件大小: {self._get_file_size(self.config.output_vector)}")
        
        return output_files
    
    def _write_raster_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float]) -> bool:
        """写入栅格输出"""
        min_lon, min_lat, max_lon, max_lat = bounds
        height, width = grid_data.shape
        
        # 创建仿射变换
        transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)
        
        # 获取CRS
        crs = self._get_output_crs()
        
        # 写入文件
        writer = RasterWriter(
            self.config.output_raster,
            grid_data,
            transform,
            crs=crs,
            nodata=self.config.nodata_value,
            dtype=self.config.output_dtype,
            compress=self.config.compression
        )
        
        return writer.write(self.config.create_overview)

    def _get_integer_raster_path(self, raster_path: str) -> str:
        """获取整型栅格文件路径"""
        import os
        dir_name = os.path.dirname(raster_path)
        base_name = os.path.basename(raster_path)
        name, ext = os.path.splitext(base_name)
        return os.path.join(dir_name, f"int_{name}{ext}")

    def _write_integer_raster_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float], output_path: str) -> bool:
        """写入整型栅格输出"""
        min_lon, min_lat, max_lon, max_lat = bounds
        height, width = grid_data.shape

        # 将浮点数据转换为整数（类似ArcPy的Int()函数）
        # 处理NaN值，将其设为0
        int_grid_data = np.where(np.isnan(grid_data), 0, np.floor(grid_data)).astype(np.int32)

        # 创建仿射变换
        transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)

        # 获取CRS
        crs = self._get_output_crs()

        # 写入文件
        writer = RasterWriter(
            output_path,
            int_grid_data,
            transform,
            crs=crs,
            nodata=0,  # 整型文件使用0作为无效值
            dtype='int32',
            compress=self.config.compression
        )

        return writer.write(self.config.create_overview)
    
    def _write_vector_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float]) -> bool:
        """写入矢量输出"""
        min_lon, min_lat, max_lon, max_lat = bounds
        height, width = grid_data.shape
        crs = self._get_output_crs()

        # 创建仿射变换
        from data.raster_io import RasterWriter
        transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)

        writer = VectorWriter(self.config.output_vector, crs)

        # 使用raster_to_polygons方法提取实际形状，而不是生成正方形网格
        return writer.raster_to_polygons(
            grid_data,
            transform,
            mask_value=0,  # 将0值作为掩码值
            progress_callback=self._update_progress
        )
    
    def _get_processing_bounds(self) -> Tuple[float, float, float, float]:
        """获取处理边界"""
        # 这里应该从输入数据获取边界
        # 简化实现，实际应该从 _read_input_data 的结果获取
        with RasterReader(self.config.input_file) as reader:
            return reader.get_bounds()
    
    def _get_output_crs(self):
        """获取输出坐标系"""
        if self.config.preserve_crs:
            with RasterReader(self.config.input_file) as reader:
                return reader.get_crs()
        return None
    
    def _calculate_statistics(self, grid_data: np.ndarray) -> Dict[str, float]:
        """计算统计信息"""
        valid_data = grid_data[~np.isnan(grid_data)]
        
        if valid_data.size == 0:
            return {'count': 0}
        
        return {
            'count': int(valid_data.size),
            'min': float(np.min(valid_data)),
            'max': float(np.max(valid_data)),
            'mean': float(np.mean(valid_data)),
            'std': float(np.std(valid_data))
        }
