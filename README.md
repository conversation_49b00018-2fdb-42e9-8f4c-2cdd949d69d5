# 北斗网格转换工具 v2.0

一个功能强大的北斗网格系统转换工具，支持将栅格数据转换为北斗网格格式，并输出为栅格或矢量文件。

## 🌟 主要特性

- **无ArcGIS依赖**: 完全基于开源库(GDAL, rasterio, fiona)实现
- **多种聚合方法**: 支持15+种数据聚合算法(最大值、平均值、中位数等)
- **现代化GUI**: 基于tkinter的用户友好图形界面
- **命令行支持**: 完整的CLI接口，支持批处理
- **多格式输出**: 同时支持栅格(.tif)和矢量(.shp)输出
- **高性能处理**: 内存优化、分块处理、可选并行计算
- **完整的北斗网格**: 支持1-10级北斗网格系统

## 📋 系统要求

- Python 3.8+
- Windows/Linux/macOS
- 推荐内存: 4GB+

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用conda (推荐，特别是GDAL)
conda install -c conda-forge gdal rasterio fiona shapely pyproj numpy scipy
```

### 2. 运行程序

#### GUI模式 (推荐)
```bash
python main.py
```

#### 命令行模式
```bash
# 基本用法
python main.py --cli -i input.tif -or output.tif -ov output.shp

# 完整参数示例
python main.py --cli -i input.tif -or output.tif -ov output.shp \
  --grid-level 8 --aggregation-method mean --window-size 7 \
  --output-dtype float32 --compression lzw --parallel --verbose
```

## 📖 使用说明

### GUI界面使用

1. **选择输入文件**: 点击"浏览"选择栅格文件
2. **设置输出**: 选择输出目录和文件名，选择输出格式
3. **配置参数**: 
   - 网格级别: 1-10级北斗网格
   - 聚合方法: max, min, mean, median等
   - 窗口大小: 数据聚合窗口
   - 其他高级参数
4. **开始处理**: 点击"开始处理"按钮
5. **查看结果**: 在结果区域查看处理统计信息

### 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-i, --input` | 输入栅格文件路径 | 必需 |
| `-or, --output-raster` | 输出栅格文件路径 | 可选 |
| `-ov, --output-vector` | 输出矢量文件路径 | 可选 |
| `--grid-level` | 网格级别 (1-10) | 7 |
| `--aggregation-method` | 聚合方法 | max |
| `--window-size` | 窗口大小 | 5 |
| `--boundary-handling` | 边界处理方式 | edge |
| `--output-dtype` | 输出数据类型 | float32 |
| `--compression` | 压缩方式 | lzw |
| `--parallel` | 启用并行处理 | False |
| `--max-memory` | 最大内存使用(GB) | 4.0 |
| `-v, --verbose` | 详细输出 | False |

## 🔧 技术架构

### 核心模块

- **core/**: 核心算法模块
  - `grid_calculator.py`: 北斗网格计算
  - `aggregation.py`: 数据聚合算法
  - `coordinate_utils.py`: 坐标转换工具

- **data/**: 数据处理模块
  - `raster_io.py`: 栅格数据读写
  - `vector_io.py`: 矢量数据写入
  - `data_validator.py`: 数据验证

- **gui/**: 图形界面模块
  - `main_window.py`: 主窗口
  - `parameter_panel.py`: 参数配置面板
  - `progress_dialog.py`: 进度对话框

### 支持的聚合方法

- `max`: 最大值
- `min`: 最小值  
- `mean`: 平均值
- `median`: 中位数
- `mode`: 众数
- `std`: 标准差
- `var`: 方差
- `sum`: 求和
- `count`: 计数
- `range`: 极差
- `percentile_25`: 25%分位数
- `percentile_75`: 75%分位数
- `percentile_90`: 90%分位数
- `percentile_95`: 95%分位数
- `percentile_99`: 99%分位数

### 支持的文件格式

**输入格式**:
- GeoTIFF (.tif, .tiff)
- ERDAS IMAGINE (.img)
- NetCDF (.nc)
- HDF5 (.h5, .hdf)

**输出格式**:
- 栅格: GeoTIFF (.tif)
- 矢量: Shapefile (.shp)

## 🎯 北斗网格系统

北斗网格系统是中国自主的地理网格编码系统，具有以下特点:

| 级别 | 网格大小 | 描述 |
|------|----------|------|
| 1 | 约111km | 省级区域 |
| 2 | 约55.5km | 地市级区域 |
| 3 | 约27.75km | 县级区域 |
| 4 | 约13.875km | 乡镇级区域 |
| 5 | 约6.9375km | 村级区域 |
| 6 | 约3.46875km | 社区级区域 |
| 7 | 约1.734375km | 街道级区域 |
| 8 | 约867.1875m | 建筑群级区域 |
| 9 | 约433.59375m | 建筑级区域 |
| 10 | 约216.796875m | 精细级区域 |

## 🔍 示例

### 处理气象数据
```bash
python main.py --cli \
  -i temperature_data.tif \
  -or temperature_grid.tif \
  -ov temperature_grid.shp \
  --grid-level 6 \
  --aggregation-method mean \
  --window-size 5
```

### 处理人口密度数据
```bash
python main.py --cli \
  -i population_density.tif \
  -or population_grid.tif \
  --grid-level 7 \
  --aggregation-method sum \
  --parallel
```

## 🐛 故障排除

### 常见问题

1. **GDAL安装失败**
   ```bash
   # 使用conda安装 (推荐)
   conda install -c conda-forge gdal
   
   # 或在Windows上下载预编译包
   # https://www.lfd.uci.edu/~gohlke/pythonlibs/
   ```

2. **内存不足错误**
   - 减小`--max-memory`参数
   - 使用更小的`--chunk-size`
   - 选择较低的网格级别

3. **处理速度慢**
   - 启用`--parallel`并行处理
   - 增加`--max-memory`参数
   - 使用SSD存储

4. **GUI无法启动**
   - 确保安装了tkinter: `pip install tkinter`
   - 在Linux上: `sudo apt-get install python3-tk`

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系:
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**北斗网格转换工具 v2.0** - 让地理数据处理更简单！
