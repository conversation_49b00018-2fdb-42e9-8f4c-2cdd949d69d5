#!/usr/bin/env python3
"""
测试窗口居中和时间估算功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog
from gui.main_window import MainWindow


def test_window_centering():
    """测试窗口居中功能"""
    print("测试窗口居中功能...")
    
    # 测试主窗口居中
    app = MainWindow()
    
    # 显示主窗口位置信息
    app.root.update_idletasks()
    x = app.root.winfo_x()
    y = app.root.winfo_y()
    width = app.root.winfo_width()
    height = app.root.winfo_height()
    screen_width = app.root.winfo_screenwidth()
    screen_height = app.root.winfo_screenheight()
    
    print(f"主窗口位置: ({x}, {y})")
    print(f"主窗口大小: {width}x{height}")
    print(f"屏幕大小: {screen_width}x{screen_height}")
    print(f"是否居中: X={abs(x - (screen_width-width)//2) < 10}, Y={abs(y - (screen_height-height)//2) < 10}")
    
    def test_progress_dialog():
        """测试进度对话框居中"""
        dialog = ProgressDialog(app.root)
        dialog.show()
        
        # 显示对话框位置信息
        dialog.dialog.update_idletasks()
        dx = dialog.dialog.winfo_x()
        dy = dialog.dialog.winfo_y()
        dwidth = dialog.dialog.winfo_width()
        dheight = dialog.dialog.winfo_height()
        
        print(f"\n进度对话框位置: ({dx}, {dy})")
        print(f"进度对话框大小: {dwidth}x{dheight}")
        print(f"对话框是否居中: X={abs(dx - (screen_width-dwidth)//2) < 10}, Y={abs(dy - (screen_height-dheight)//2) < 10}")
        
        def simulate_progress():
            """模拟进度更新"""
            stages = [
                ("初始化", 0, 10),
                ("读取数据", 10, 30),
                ("处理数据", 30, 70),
                ("聚合数据", 70, 90),
                ("写入结果", 90, 100)
            ]
            
            for stage_name, start_percent, end_percent in stages:
                print(f"\n开始阶段: {stage_name}")
                for percent in range(start_percent, end_percent + 1, 2):
                    if dialog.is_cancelled:
                        break
                    
                    detail = f"正在{stage_name}... ({percent}%)"
                    dialog.update_progress(percent, stage_name, detail)
                    time.sleep(0.3)  # 模拟处理时间
                
                if dialog.is_cancelled:
                    break
            
            if not dialog.is_cancelled:
                time.sleep(2)  # 显示完成状态
                dialog.close()
        
        # 设置取消回调
        dialog.set_cancel_callback(lambda: print("用户取消了处理"))
        
        # 在新线程中运行进度模拟
        thread = threading.Thread(target=simulate_progress)
        thread.daemon = True
        thread.start()
    
    # 添加测试按钮
    test_frame = ttk.Frame(app.root)
    test_frame.pack(pady=20)
    
    ttk.Button(
        test_frame,
        text="测试进度对话框居中和时间估算",
        command=test_progress_dialog,
        width=30
    ).pack()
    
    app.run()


def test_time_estimation_accuracy():
    """测试时间估算准确性"""
    root = tk.Tk()
    root.title("时间估算准确性测试")
    root.geometry("800x600")
    
    # 居中显示
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 800) // 2
    y = (screen_height - 600) // 2
    root.geometry(f"800x600+{x}+{y}")
    
    frame = ttk.Frame(root, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame,
        text="时间估算准确性测试",
        font=("Arial", 16, "bold")
    ).pack(pady=(0, 20))
    
    def test_fast_progress():
        """测试快速进度"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def fast_task():
            for i in range(101):
                if dialog.is_cancelled:
                    break
                dialog.update_progress(i, f"快速任务", f"步骤 {i}/100")
                time.sleep(0.05)  # 快速进度
            
            if not dialog.is_cancelled:
                time.sleep(1)
                dialog.close()
        
        dialog.set_cancel_callback(lambda: print("快速任务被取消"))
        threading.Thread(target=fast_task, daemon=True).start()
    
    def test_slow_progress():
        """测试慢速进度"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def slow_task():
            for i in range(101):
                if dialog.is_cancelled:
                    break
                dialog.update_progress(i, f"慢速任务", f"步骤 {i}/100")
                time.sleep(0.2)  # 慢速进度
            
            if not dialog.is_cancelled:
                time.sleep(1)
                dialog.close()
        
        dialog.set_cancel_callback(lambda: print("慢速任务被取消"))
        threading.Thread(target=slow_task, daemon=True).start()
    
    def test_variable_speed():
        """测试变速进度"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def variable_task():
            # 第一阶段：快速
            for i in range(0, 30):
                if dialog.is_cancelled:
                    break
                dialog.update_progress(i, "快速阶段", f"快速处理 {i}/30")
                time.sleep(0.05)
            
            # 第二阶段：慢速
            for i in range(30, 70):
                if dialog.is_cancelled:
                    break
                dialog.update_progress(i, "慢速阶段", f"仔细处理 {i}/70")
                time.sleep(0.3)
            
            # 第三阶段：中等速度
            for i in range(70, 101):
                if dialog.is_cancelled:
                    break
                dialog.update_progress(i, "收尾阶段", f"最后处理 {i}/100")
                time.sleep(0.1)
            
            if not dialog.is_cancelled:
                time.sleep(1)
                dialog.close()
        
        dialog.set_cancel_callback(lambda: print("变速任务被取消"))
        threading.Thread(target=variable_task, daemon=True).start()
    
    # 测试按钮
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=20)
    
    ttk.Button(
        button_frame,
        text="快速进度测试",
        command=test_fast_progress,
        width=20
    ).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(
        button_frame,
        text="慢速进度测试",
        command=test_slow_progress,
        width=20
    ).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(
        button_frame,
        text="变速进度测试",
        command=test_variable_speed,
        width=20
    ).pack(side=tk.LEFT, padx=10)
    
    ttk.Label(
        frame,
        text="观察要点：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(30, 10))
    
    observations = [
        "• 进度达到5%后开始显示预计剩余时间",
        "• 不同阶段的时间估算会根据当前速度调整",
        "• 变速任务中时间估算应该适应速度变化",
        "• 所有对话框都应该在屏幕中央显示"
    ]
    
    for obs in observations:
        ttk.Label(
            frame,
            text=obs,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    ttk.Button(
        frame,
        text="退出",
        command=root.quit,
        width=20
    ).pack(pady=(30, 0))
    
    root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("窗口居中和时间估算功能测试")
    print("=" * 60)
    
    choice = input("""
请选择测试项目:
1. 测试窗口居中功能（主窗口和进度对话框）
2. 测试时间估算准确性
3. 退出

请输入选择 (1-3): """).strip()
    
    if choice == "1":
        print("启动窗口居中功能测试...")
        test_window_centering()
    elif choice == "2":
        print("启动时间估算准确性测试...")
        test_time_estimation_accuracy()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
