#!/usr/bin/env python3
"""
测试详细进度显示和取消功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog
from core.aggregation import DataAggregator, AggregationMethod


def test_detailed_aggregation_progress():
    """测试详细的聚合进度显示"""
    root = tk.Tk()
    root.title("详细聚合进度测试")
    root.geometry("600x400")
    
    def start_aggregation_test():
        """开始聚合测试"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def simulate_aggregation():
            """模拟聚合处理"""
            try:
                # 创建测试数据
                test_data = np.random.rand(200, 200) * 100  # 200x200的测试数据
                
                # 创建聚合器
                aggregator = DataAggregator(
                    method=AggregationMethod.MEAN,
                    window_size=5,
                    boundary_handling="constant"
                )
                
                # 模拟进度回调
                def progress_callback(percent, message, detail):
                    if not dialog.is_cancelled:
                        dialog.update_progress(percent, message, detail)
                
                # 模拟取消检查
                def cancel_check():
                    if dialog.is_cancelled:
                        raise InterruptedError("处理已被用户取消")
                
                # 执行聚合（这会显示详细进度）
                dialog.update_progress(0, "开始数据聚合", "初始化聚合器...")
                time.sleep(0.5)
                
                dialog.update_progress(10, "准备数据", f"数据尺寸: {test_data.shape}")
                time.sleep(0.5)
                
                # 执行实际聚合
                result = aggregator.aggregate(test_data, progress_callback=progress_callback, cancel_check=cancel_check)
                
                if not dialog.is_cancelled:
                    dialog.update_progress(100, "聚合完成", f"输出尺寸: {result.shape}")
                    time.sleep(2)
                
            except InterruptedError:
                dialog.update_progress(0, "处理已取消", "用户取消了聚合操作")
                time.sleep(1)
            except Exception as e:
                dialog.update_progress(0, "处理失败", f"错误: {str(e)}")
                time.sleep(2)
            finally:
                if not dialog.is_cancelled:
                    dialog.close()
        
        # 设置取消回调
        def on_cancel():
            print("用户取消了聚合处理！")
        
        dialog.set_cancel_callback(on_cancel)
        
        # 在新线程中运行聚合
        thread = threading.Thread(target=simulate_aggregation)
        thread.daemon = True
        thread.start()
    
    # 创建测试界面
    frame = ttk.Frame(root, padding="30")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame, 
        text="详细聚合进度测试", 
        font=("Arial", 18, "bold")
    ).pack(pady=(0, 30))
    
    ttk.Label(
        frame,
        text="测试特性：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(0, 10))
    
    features = [
        "• 实时显示聚合进度（每1000像素更新）",
        "• 显示已处理像素数量和百分比",
        "• 支持取消操作和立即响应",
        "• 详细的状态信息显示",
        "• 改进的取消按钮行为"
    ]
    
    for feature in features:
        ttk.Label(
            frame,
            text=feature,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    ttk.Button(
        frame,
        text="开始聚合进度测试",
        command=start_aggregation_test,
        width=30
    ).pack(pady=(30, 10))
    
    ttk.Button(
        frame,
        text="退出",
        command=root.quit,
        width=30
    ).pack(pady=10)
    
    root.mainloop()


def test_cancel_functionality():
    """测试取消功能"""
    root = tk.Tk()
    root.title("取消功能测试")
    root.geometry("600x400")
    
    def start_cancel_test():
        """开始取消测试"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def long_running_task():
            """长时间运行的任务"""
            try:
                for i in range(100):
                    if dialog.is_cancelled:
                        raise InterruptedError("任务已被取消")
                    
                    dialog.update_progress(
                        i + 1, 
                        f"执行步骤 {i + 1}/100", 
                        f"当前进度: {i + 1}%，预计剩余时间: {100 - i - 1} 秒"
                    )
                    time.sleep(1)  # 模拟耗时操作
                
                dialog.update_progress(100, "任务完成", "所有步骤已完成！")
                time.sleep(2)
                
            except InterruptedError:
                print("任务被用户取消")
            finally:
                if not dialog.is_cancelled:
                    dialog.close()
        
        # 设置取消回调
        def on_cancel():
            print("立即取消任务！")
            # 这里可以添加清理逻辑
        
        dialog.set_cancel_callback(on_cancel)
        
        # 在新线程中运行任务
        thread = threading.Thread(target=long_running_task)
        thread.daemon = True
        thread.start()
    
    # 创建测试界面
    frame = ttk.Frame(root, padding="30")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame, 
        text="取消功能测试", 
        font=("Arial", 18, "bold")
    ).pack(pady=(0, 30))
    
    ttk.Label(
        frame,
        text="说明：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(0, 10))
    
    instructions = [
        "1. 点击开始测试按钮启动长时间任务",
        "2. 任务会运行100秒（每秒更新一次进度）",
        "3. 在任务运行期间点击'取消处理'按钮",
        "4. 观察取消按钮是否立即响应",
        "5. 对话框应该立即关闭而不是一直显示'正在取消'"
    ]
    
    for instruction in instructions:
        ttk.Label(
            frame,
            text=instruction,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    ttk.Button(
        frame,
        text="开始取消功能测试",
        command=start_cancel_test,
        width=30
    ).pack(pady=(30, 10))
    
    ttk.Button(
        frame,
        text="退出",
        command=root.quit,
        width=30
    ).pack(pady=10)
    
    root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("详细进度显示和取消功能测试")
    print("=" * 60)
    
    choice = input("""
请选择测试项目:
1. 测试详细聚合进度显示
2. 测试取消功能
3. 退出

请输入选择 (1-3): """).strip()
    
    if choice == "1":
        print("启动详细聚合进度测试...")
        test_detailed_aggregation_progress()
    elif choice == "2":
        print("启动取消功能测试...")
        test_cancel_functionality()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
