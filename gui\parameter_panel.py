"""
参数配置面板
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.aggregation import AggregationMethod


class ParameterPanel:
    """参数配置面板"""
    
    def __init__(self, parent):
        """
        初始化参数面板
        
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.frame = ttk.LabelFrame(parent, text="处理参数", padding="10")
        
        # 初始化变量
        self._init_variables()
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        
        # 设置默认值
        self._set_defaults()
    
    def _init_variables(self):
        """初始化变量"""
        # 网格参数
        self.grid_level_var = tk.IntVar(value=7)
        
        # 聚合参数
        self.aggregation_method_var = tk.StringVar(value="max")
        self.window_size_var = tk.IntVar(value=5)
        self.boundary_handling_var = tk.StringVar(value="edge")
        
        # 输出参数
        self.output_dtype_var = tk.StringVar(value="float32")
        self.compression_var = tk.StringVar(value="lzw")
        self.create_overview_var = tk.BooleanVar(value=True)
        
        # 处理参数
        self.parallel_processing_var = tk.BooleanVar(value=False)
        self.max_memory_var = tk.DoubleVar(value=4.0)
        self.chunk_size_var = tk.IntVar(value=1024)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 网格参数组
        self.grid_frame = ttk.LabelFrame(self.frame, text="网格参数", padding="5")
        
        # 网格级别
        self.grid_level_label = ttk.Label(self.grid_frame, text="网格级别 (1-10):")
        self.grid_level_scale = ttk.Scale(
            self.grid_frame, 
            from_=1, to=10, 
            orient=tk.HORIZONTAL,
            variable=self.grid_level_var,
            command=self._on_grid_level_change
        )
        self.grid_level_value = ttk.Label(self.grid_frame, text="7")
        
        # 网格级别描述
        self.grid_desc_var = tk.StringVar()
        self.grid_desc_label = ttk.Label(
            self.grid_frame,
            textvariable=self.grid_desc_var,
            font=("Arial", 8),
            foreground="blue",
            wraplength=300
        )
        
        # 聚合参数组
        self.agg_frame = ttk.LabelFrame(self.frame, text="聚合参数", padding="5")
        
        # 聚合方法
        self.agg_method_label = ttk.Label(self.agg_frame, text="聚合方法:")
        self.agg_method_combo = ttk.Combobox(
            self.agg_frame,
            textvariable=self.aggregation_method_var,
            values=self._get_aggregation_methods(),
            state="readonly",
            width=15
        )
        self.agg_method_combo.bind('<<ComboboxSelected>>', self._on_aggregation_method_change)
        
        # 窗口大小
        self.window_size_label = ttk.Label(self.agg_frame, text="窗口大小:")
        self.window_size_spin = ttk.Spinbox(
            self.agg_frame,
            from_=3, to=21, increment=2,
            textvariable=self.window_size_var,
            width=10,
            command=self._on_window_size_change
        )
        self.window_size_spin.bind('<KeyRelease>', self._on_window_size_change)
        
        # 边界处理
        self.boundary_label = ttk.Label(self.agg_frame, text="边界处理:")
        self.boundary_combo = ttk.Combobox(
            self.agg_frame,
            textvariable=self.boundary_handling_var,
            values=["edge", "constant", "reflect", "wrap"],
            state="readonly",
            width=15
        )
        
        # 输出参数组
        self.output_frame = ttk.LabelFrame(self.frame, text="输出参数", padding="5")
        
        # 数据类型
        self.dtype_label = ttk.Label(self.output_frame, text="数据类型:")
        self.dtype_combo = ttk.Combobox(
            self.output_frame,
            textvariable=self.output_dtype_var,
            values=["float32", "float64", "int16", "int32"],
            state="readonly",
            width=15
        )
        self.dtype_combo.bind('<<ComboboxSelected>>', self._on_dtype_change)
        
        # 压缩方式
        self.compression_label = ttk.Label(self.output_frame, text="压缩方式:")
        self.compression_combo = ttk.Combobox(
            self.output_frame,
            textvariable=self.compression_var,
            values=["none", "lzw", "deflate", "packbits"],
            state="readonly",
            width=15
        )
        self.compression_combo.bind('<<ComboboxSelected>>', self._on_compression_change)
        
        # 创建概览图
        self.overview_check = ttk.Checkbutton(
            self.output_frame,
            text="创建概览图",
            variable=self.create_overview_var
        )


        
        # 高级参数组
        self.advanced_frame = ttk.LabelFrame(self.frame, text="高级参数", padding="5")
        
        # 并行处理
        self.parallel_check = ttk.Checkbutton(
            self.advanced_frame,
            text="启用并行处理",
            variable=self.parallel_processing_var
        )
        
        # 最大内存
        self.memory_label = ttk.Label(self.advanced_frame, text="最大内存 (GB):")
        self.memory_spin = ttk.Spinbox(
            self.advanced_frame,
            from_=1.0, to=32.0, increment=0.5,
            textvariable=self.max_memory_var,
            width=10,
            format="%.1f"
        )
        
        # 块大小
        self.chunk_label = ttk.Label(self.advanced_frame, text="块大小:")
        self.chunk_spin = ttk.Spinbox(
            self.advanced_frame,
            from_=256, to=4096, increment=256,
            textvariable=self.chunk_size_var,
            width=10
        )

        # 初始化动态说明
        self._update_dynamic_help()
    
    def _setup_layout(self):
        """设置布局"""
        # 网格参数组
        self.grid_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.grid_frame.grid_columnconfigure(1, weight=1)
        
        self.grid_level_label.grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.grid_level_scale.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        self.grid_level_value.grid(row=0, column=2)
        self.grid_desc_label.grid(row=1, column=0, columnspan=3, sticky="w", pady=(5, 0))
        
        # 聚合参数组
        self.agg_frame.grid(row=1, column=0, sticky="ew", padx=(0, 10), pady=(0, 10))
        self.agg_frame.grid_columnconfigure(1, weight=1)

        self.agg_method_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.agg_method_combo.grid(row=0, column=1, sticky="w", pady=(0, 5), padx=(5, 0))

        self.window_size_label.grid(row=1, column=0, sticky="w", pady=(0, 5))
        self.window_size_spin.grid(row=1, column=1, sticky="w", pady=(0, 5), padx=(5, 0))

        self.boundary_label.grid(row=2, column=0, sticky="w")
        self.boundary_combo.grid(row=2, column=1, sticky="w", padx=(5, 0))
        
        # 输出参数组
        self.output_frame.grid(row=1, column=1, sticky="ew", pady=(0, 10))
        self.output_frame.grid_columnconfigure(1, weight=1)

        self.dtype_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.dtype_combo.grid(row=0, column=1, sticky="w", pady=(0, 5), padx=(5, 0))

        self.compression_label.grid(row=1, column=0, sticky="w", pady=(0, 5))
        self.compression_combo.grid(row=1, column=1, sticky="w", pady=(0, 5), padx=(5, 0))

        self.overview_check.grid(row=2, column=0, columnspan=2, sticky="w")
        
        # 高级参数组
        self.advanced_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.advanced_frame.grid_columnconfigure(1, weight=1)

        self.parallel_check.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 5))

        self.memory_label.grid(row=1, column=0, sticky="w", padx=(0, 10))
        self.memory_spin.grid(row=1, column=1, sticky="w")

        self.chunk_label.grid(row=1, column=2, sticky="w", padx=(20, 10))
        self.chunk_spin.grid(row=1, column=3, sticky="w")

        # 设置权重
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_columnconfigure(1, weight=1)
    
    def _set_defaults(self):
        """设置默认值"""
        self._update_grid_description()
        self._update_dynamic_help()
    
    def _get_aggregation_methods(self):
        """获取聚合方法列表"""
        methods = []
        for method in AggregationMethod:
            methods.append(method.value)
        return methods
    
    def _on_grid_level_change(self, value):
        """网格级别改变事件"""
        level = int(float(value))
        self.grid_level_value.config(text=str(level))
        self._update_grid_description()

    def _on_aggregation_method_change(self, event=None):
        """聚合方法改变时的回调"""
        pass

    def _on_window_size_change(self, event=None):
        """窗口大小改变时的回调"""
        pass

    def _on_dtype_change(self, event=None):
        """数据类型改变时的回调"""
        pass

    def _on_compression_change(self, event=None):
        """压缩方式改变时的回调"""
        pass
    
    def _update_grid_description(self):
        """更新网格级别描述"""
        from core.grid_calculator import BeidouGridCalculator
        
        level = self.grid_level_var.get()
        calculator = BeidouGridCalculator()
        
        if level in calculator.GRID_LEVELS:
            desc = calculator.GRID_LEVELS[level].description
            self.grid_desc_var.set(desc)
        else:
            self.grid_desc_var.set("")
    
    # Getter方法
    def get_grid_level(self) -> int:
        """获取网格级别"""
        return self.grid_level_var.get()
    
    def get_aggregation_method(self) -> str:
        """获取聚合方法"""
        return self.aggregation_method_var.get()
    
    def get_window_size(self) -> int:
        """获取窗口大小"""
        return self.window_size_var.get()
    
    def get_boundary_handling(self) -> str:
        """获取边界处理方式"""
        return self.boundary_handling_var.get()
    
    def get_output_dtype(self) -> str:
        """获取输出数据类型"""
        return self.output_dtype_var.get()
    
    def get_compression(self) -> str:
        """获取压缩方式"""
        return self.compression_var.get()
    
    def get_create_overview(self) -> bool:
        """获取是否创建概览图"""
        return self.create_overview_var.get()
    
    def get_parallel_processing(self) -> bool:
        """获取是否启用并行处理"""
        return self.parallel_processing_var.get()
    
    def get_max_memory(self) -> float:
        """获取最大内存"""
        return self.max_memory_var.get()
    
    def get_chunk_size(self) -> int:
        """获取块大小"""
        return self.chunk_size_var.get()
    
    def reset_to_defaults(self):
        """重置为默认值"""
        self.grid_level_var.set(7)
        self.aggregation_method_var.set("max")
        self.window_size_var.set(5)
        self.boundary_handling_var.set("edge")
        self.output_dtype_var.set("float32")
        self.compression_var.set("lzw")
        self.create_overview_var.set(True)
        self.output_integer_raster_var.set(True)
        self.parallel_processing_var.set(False)
        self.max_memory_var.set(4.0)
        self.chunk_size_var.set(1024)
        self._update_grid_description()

    def _update_dynamic_help(self):
        """更新动态说明"""
        pass


