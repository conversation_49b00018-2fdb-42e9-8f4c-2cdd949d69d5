#!/usr/bin/env python3
"""
测试改进后的GUI界面
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog, IndeterminateProgressDialog


def test_large_progress_dialog():
    """测试大尺寸进度对话框"""
    root = tk.Tk()
    root.title("大尺寸进度对话框测试")
    root.geometry("600x400")
    
    def start_test():
        """开始测试"""
        dialog = ProgressDialog(root)
        dialog.show()
        
        def simulate_processing():
            """模拟处理过程"""
            steps = [
                (5, "初始化系统", "正在加载配置文件和初始化系统组件..."),
                (15, "读取输入数据", "正在从磁盘读取大型栅格文件 (1.2GB)，请耐心等待..."),
                (25, "数据验证", "正在验证数据完整性和格式兼容性，检查坐标系统..."),
                (35, "内存分配", "正在为大数据处理分配内存缓冲区 (2.5GB)..."),
                (45, "数据预处理", "正在执行数据清洗、去噪和格式转换操作..."),
                (55, "网格计算", "正在计算北斗网格编码，当前处理级别7网格..."),
                (65, "数据聚合", "正在使用均值方法聚合数据，窗口大小: 5x5..."),
                (75, "空间分析", "正在执行空间插值和边界处理算法..."),
                (85, "结果生成", "正在生成输出栅格和矢量文件..."),
                (95, "文件写入", "正在将结果写入磁盘，压缩格式: LZW..."),
                (100, "处理完成", "所有处理步骤已完成！生成了 15,847 个有效网格单元。")
            ]
            
            for percent, message, detail in steps:
                if dialog.is_cancelled:
                    break
                
                dialog.update_progress(percent, message, detail)
                time.sleep(1.5)  # 模拟处理时间
            
            if not dialog.is_cancelled:
                time.sleep(2)  # 显示完成信息
            
            dialog.close()
        
        # 设置取消回调
        def on_cancel():
            print("用户取消了处理！")
            # 这里可以添加清理逻辑
        
        dialog.set_cancel_callback(on_cancel)
        
        # 在新线程中运行模拟处理
        thread = threading.Thread(target=simulate_processing)
        thread.daemon = True
        thread.start()
    
    # 创建测试界面
    frame = ttk.Frame(root, padding="30")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(
        frame, 
        text="改进后的进度对话框测试", 
        font=("Arial", 18, "bold")
    ).pack(pady=(0, 30))
    
    ttk.Label(
        frame,
        text="测试特性：",
        font=("Arial", 12, "bold")
    ).pack(anchor=tk.W, pady=(0, 10))
    
    features = [
        "• 更大的对话框尺寸 (700x400)",
        "• 更长的进度条 (600px)",
        "• 更大的字体和更好的可读性",
        "• 详细的状态信息显示",
        "• 实时时间显示",
        "• 改进的取消功能"
    ]
    
    for feature in features:
        ttk.Label(
            frame,
            text=feature,
            font=("Arial", 10)
        ).pack(anchor=tk.W, pady=2)
    
    ttk.Button(
        frame,
        text="开始测试大尺寸进度对话框",
        command=start_test,
        width=30
    ).pack(pady=(30, 10))
    
    ttk.Button(
        frame,
        text="退出",
        command=root.quit,
        width=30
    ).pack(pady=10)
    
    root.mainloop()


def test_main_window_size():
    """测试主窗口尺寸"""
    try:
        from gui.main_window import MainWindow
        
        print("启动改进后的主窗口...")
        print("窗口特性：")
        print("- 初始尺寸: 900x750")
        print("- 最小尺寸: 800x650")
        print("- 增加的内边距: 15px")
        print("- 可调整大小")
        
        app = MainWindow()
        app.run()
        
    except Exception as e:
        print(f"主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("=" * 60)
    print("改进后的GUI界面测试")
    print("=" * 60)
    
    choice = input("""
请选择测试项目:
1. 测试大尺寸进度对话框
2. 测试改进后的主窗口
3. 退出

请输入选择 (1-3): """).strip()
    
    if choice == "1":
        print("启动大尺寸进度对话框测试...")
        test_large_progress_dialog()
    elif choice == "2":
        print("启动改进后的主窗口测试...")
        test_main_window_size()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
