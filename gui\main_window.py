"""
主窗口界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from typing import Optional

from .parameter_panel import ParameterPanel
from .progress_dialog import ProgressDialog
from .window_utils import configure_window_properties
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
from core.aggregation import AggregationMethod


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()

        # 配置窗口属性（居中显示）
        configure_window_properties(
            window=self.root,
            title="北斗网格转换工具 v2.0",
            width=750,
            height= 780,
            min_width=750,
            min_height=780,
            resizable=True,
            center=True,
            icon_path="icon.ico"
        )

        # 初始化变量
        self.processor: Optional[BeidouGridProcessor] = None
        self.progress_dialog: Optional[ProgressDialog] = None
        self.current_processor: Optional[BeidouGridProcessor] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 设置默认值
        self._set_default_values()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="15")
        
        # 标题
        self.title_label = ttk.Label(
            self.main_frame, 
            text="北斗网格转换工具", 
            font=("Arial", 16, "bold")
        )
        
        # 输入文件选择
        self.input_frame = ttk.LabelFrame(self.main_frame, text="输入文件", padding="5")
        self.input_path_var = tk.StringVar()
        self.input_entry = ttk.Entry(self.input_frame, textvariable=self.input_path_var, width=60)
        self.input_browse_btn = ttk.Button(self.input_frame, text="浏览...", command=self._browse_input_file)
        
        # 输出设置
        self.output_frame = ttk.LabelFrame(self.main_frame, text="输出设置", padding="5")
        
        # 输出目录
        self.output_dir_var = tk.StringVar()
        self.output_dir_entry = ttk.Entry(self.output_frame, textvariable=self.output_dir_var, width=50)
        self.output_dir_btn = ttk.Button(self.output_frame, text="选择目录", command=self._browse_output_dir)
        
        # 输出文件名
        self.output_name_var = tk.StringVar(value="output")
        self.output_name_entry = ttk.Entry(self.output_frame, textvariable=self.output_name_var, width=20)
        
        # 输出格式选择变量
        self.output_raster_var = tk.BooleanVar(value=True)
        self.output_vector_var = tk.BooleanVar(value=True)
        
        # 参数面板
        self.param_panel = ParameterPanel(self.main_frame)
        
        # 控制按钮
        self.control_frame = ttk.Frame(self.main_frame)
        self.process_btn = ttk.Button(
            self.control_frame,
            text="开始处理",
            command=self._start_processing
        )
        self.reset_btn = ttk.Button(self.control_frame, text="重置", command=self._reset_form)
        self.exit_btn = ttk.Button(self.control_frame, text="退出", command=self._exit_application)
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        
        # 结果显示区域（隐藏，由进度条替代）
        # self.result_frame = ttk.LabelFrame(self.main_frame, text="处理结果", padding="5")
        # self.result_text = tk.Text(self.result_frame, height=8, width=80, wrap=tk.WORD)
        # self.result_scroll = ttk.Scrollbar(self.result_frame, orient="vertical", command=self.result_text.yview)
        # self.result_text.configure(yscrollcommand=self.result_scroll.set)
    
    def _setup_layout(self):
        """设置布局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 标题
        self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 输入文件
        self.input_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.input_frame.grid_columnconfigure(0, weight=1)
        
        ttk.Label(self.input_frame, text="栅格文件:").grid(row=0, column=0, sticky="w")
        self.input_entry.grid(row=1, column=0, sticky="ew", padx=(0, 5))
        self.input_browse_btn.grid(row=1, column=1)
        
        # 输出设置
        self.output_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.output_frame.grid_columnconfigure(0, weight=1)
        
        ttk.Label(self.output_frame, text="输出目录:").grid(row=0, column=0, sticky="w")
        self.output_dir_entry.grid(row=1, column=0, sticky="ew", padx=(0, 5))
        self.output_dir_btn.grid(row=1, column=1)
        
        ttk.Label(self.output_frame, text="文件名:").grid(row=2, column=0, sticky="w", pady=(10, 0))
        self.output_name_entry.grid(row=3, column=0, sticky="w")
        
        ttk.Label(self.output_frame, text="输出格式:").grid(row=4, column=0, sticky="w", pady=(10, 0))
        format_frame = ttk.Frame(self.output_frame)
        format_frame.grid(row=5, column=0, sticky="ew", columnspan=2)

        # 将复选框放置在format_frame中
        self.raster_check = ttk.Checkbutton(format_frame, text="栅格文件(.tif)", variable=self.output_raster_var)
        self.vector_check = ttk.Checkbutton(format_frame, text="矢量文件(.shp)", variable=self.output_vector_var)
        self.raster_check.grid(row=0, column=0, padx=(0, 20))
        self.vector_check.grid(row=0, column=1)


        
        # 参数面板
        self.param_panel.frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # 控制按钮
        self.control_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        self.process_btn.grid(row=0, column=0, padx=(0, 10))
        self.reset_btn.grid(row=0, column=1, padx=(0, 10))
        self.exit_btn.grid(row=0, column=2)
        
        # 结果显示（隐藏）
        # self.result_frame.grid(row=5, column=0, columnspan=2, sticky="nsew", pady=(0, 10))
        # self.result_frame.grid_rowconfigure(0, weight=1)
        # self.result_frame.grid_columnconfigure(0, weight=1)
        #
        # self.result_text.grid(row=0, column=0, sticky="nsew")
        # self.result_scroll.grid(row=0, column=1, sticky="ns")
        
        # 状态栏
        self.status_frame.grid(row=5, column=0, columnspan=2, sticky="ew")
        self.status_label.grid(row=0, column=0, sticky="w")
        
        # 设置权重
        self.main_frame.grid_rowconfigure(5, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
    
    def _bind_events(self):
        """绑定事件"""
        self.root.protocol("WM_DELETE_WINDOW", self._exit_application)
    
    def _set_default_values(self):
        """设置默认值"""
        # 设置默认输出目录为当前目录
        #self.output_dir_var.set(os.getcwd())
    
    def _browse_input_file(self):
        """浏览输入文件"""
        filetypes = [
            ("栅格文件", "*.tif *.tiff *.img *.nc *.hdf *.h5"),
            ("TIFF文件", "*.tif *.tiff"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择输入栅格文件",
            filetypes=filetypes
        )
        
        if filename:
            self.input_path_var.set(filename)
            # 自动设置输出目录为输入文件所在目录
            if not self.output_dir_var.get():
                self.output_dir_var.set(os.path.dirname(filename)+"/output")
    
    def _browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_var.set(directory)
    
    def _validate_inputs(self) -> bool:
        """验证输入"""
        if not self.input_path_var.get():
            messagebox.showerror("错误", "请选择输入文件")
            return False
        
        if not os.path.exists(self.input_path_var.get()):
            messagebox.showerror("错误", "输入文件不存在")
            return False
        
        if not self.output_dir_var.get():
            messagebox.showerror("错误", "请选择输出目录")
            return False
        
        if not self.output_name_var.get():
            messagebox.showerror("错误", "请输入输出文件名")
            return False
        
        if not (self.output_raster_var.get() or self.output_vector_var.get()):
            messagebox.showerror("错误", "请至少选择一种输出格式")
            return False
        
        return True
    
    def _create_config(self) -> ProcessingConfig:
        """创建处理配置"""
        output_dir = self.output_dir_var.get()
        output_name = self.output_name_var.get()
        
        config = ProcessingConfig(
            input_file=self.input_path_var.get(),
            grid_level=self.param_panel.get_grid_level(),
            aggregation_method=self.param_panel.get_aggregation_method(),
            window_size=self.param_panel.get_window_size(),
            boundary_handling=self.param_panel.get_boundary_handling(),
            output_dtype=self.param_panel.get_output_dtype(),
            compression=self.param_panel.get_compression(),
            create_overview=self.param_panel.get_create_overview(),
            output_integer_raster=self._should_output_integer_raster(),
            parallel_processing=self.param_panel.get_parallel_processing(),
            max_memory_gb=self.param_panel.get_max_memory(),
            verbose=True
        )
        
        # 设置输出文件路径
        if self.output_raster_var.get():
            config.output_raster = os.path.join(output_dir, f"{output_name}.tif")

        if self.output_vector_var.get():
            config.output_vector = os.path.join(output_dir, f"{output_name}.shp")
        
        return config

    def _should_output_integer_raster(self) -> bool:
        """判断是否应该输出整型栅格"""
        # 当数据类型选择为整型时，输出整型栅格
        dtype = self.param_panel.get_output_dtype()
        return dtype in ["int16", "int32"]

    def _start_processing(self):
        """开始处理"""
        if not self._validate_inputs():
            return
        
        # 禁用处理按钮
        self.process_btn.config(state="disabled")
        self.status_var.set("正在处理...")
        
        # 清空结果显示（隐藏）
        # self.result_text.delete(1.0, tk.END)
        
        # 创建配置
        config = self._create_config()
        
        # 在新线程中执行处理
        thread = threading.Thread(target=self._process_thread, args=(config,))
        thread.daemon = True
        thread.start()
    
    def _process_thread(self, config: ProcessingConfig):
        """处理线程"""
        try:
            # 创建处理器
            processor = BeidouGridProcessor(config)
            self.current_processor = processor  # 保存当前处理器引用

            # 创建进度对话框
            self.progress_dialog = ProgressDialog(self.root)
            processor.set_progress_callback(self.progress_dialog.update_progress)

            # 设置取消回调
            self.progress_dialog.set_cancel_callback(self._cancel_processing)

            # 显示进度对话框
            self.root.after(0, self.progress_dialog.show)

            # 执行处理
            result = processor.process()

            # 更新界面
            self.root.after(0, self._processing_completed, result)

        except InterruptedError:
            # 处理被取消
            self.root.after(0, self._processing_cancelled)
        except Exception as e:
            self.root.after(0, self._processing_error, str(e))
        finally:
            self.current_processor = None
    
    def _processing_completed(self, result: dict):
        """处理完成"""
        # 关闭进度对话框
        if self.progress_dialog:
            self.progress_dialog.close()
        
        # 启用处理按钮
        self.process_btn.config(state="normal")
        
        if result['success']:
            self.status_var.set("处理完成")
            
            # 显示结果
            self._display_result(result)
            
            messagebox.showinfo("成功", "处理完成！")
        else:
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"处理失败: {result.get('error', '未知错误')}")
    
    def _processing_error(self, error_msg: str):
        """处理错误"""
        # 关闭进度对话框
        if self.progress_dialog:
            self.progress_dialog.close()

        # 启用处理按钮
        self.process_btn.config(state="normal")
        self.status_var.set("处理失败")

        messagebox.showerror("错误", f"处理失败: {error_msg}")

    def _processing_cancelled(self):
        """处理被取消"""
        # 关闭进度对话框
        if self.progress_dialog:
            self.progress_dialog.close()

        # 启用处理按钮
        self.process_btn.config(state="normal")

        self.status_var.set("处理已取消")
        messagebox.showinfo("取消", "处理已被用户取消")

    def _cancel_processing(self):
        """取消处理"""
        if self.current_processor:
            self.current_processor.cancel()
    
    def _display_result(self, result: dict):
        """显示结果（隐藏文本显示，由进度条替代）"""
        # self.result_text.delete(1.0, tk.END)
        #
        # text = "处理结果:\n"
        # text += f"输入数据形状: {result.get('input_shape', 'N/A')}\n"
        # text += f"输出数据形状: {result.get('output_shape', 'N/A')}\n"
        # text += f"网格级别: {result.get('grid_level', 'N/A')}\n"
        # text += f"聚合方法: {result.get('aggregation_method', 'N/A')}\n"
        #
        # if 'output_files' in result:
        #     text += "\n输出文件:\n"
        #     for file_type, file_path in result['output_files'].items():
        #         text += f"  {file_type}: {file_path}\n"
        #
        # if 'statistics' in result:
        #     stats = result['statistics']
        #     text += f"\n统计信息:\n"
        #     text += f"  有效像素数: {stats.get('count', 'N/A')}\n"
        #     text += f"  最小值: {stats.get('min', 'N/A'):.4f}\n"
        #     text += f"  最大值: {stats.get('max', 'N/A'):.4f}\n"
        #     text += f"  平均值: {stats.get('mean', 'N/A'):.4f}\n"
        #     text += f"  标准差: {stats.get('std', 'N/A'):.4f}\n"
        #
        # self.result_text.insert(tk.END, text)

        # 只更新状态栏显示处理完成
        self.status_var.set("处理完成")
    
    def _reset_form(self):
        """重置表单"""
        self.input_path_var.set("")
        self.output_dir_var.set(os.getcwd())
        self.output_name_var.set("output")
        self.output_raster_var.set(True)
        self.output_vector_var.set(True)
        self.param_panel.reset_to_defaults()
        # self.result_text.delete(1.0, tk.END)
        self.status_var.set("就绪")
    
    def _exit_application(self):
        """退出应用程序"""
        if messagebox.askokcancel("退出", "确定要退出吗？"):
            self.root.quit()
            self.root.destroy()


    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()
