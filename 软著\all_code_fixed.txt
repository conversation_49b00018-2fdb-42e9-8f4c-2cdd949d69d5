﻿================================================================================
北斗网格转换工具 - 完整源代码（修复中文编码）
================================================================================
生成时间: 2025-07-31 13:51:46
文件总数: 25
================================================================================

文件列表:
   1. Script\beidou_transfrom_shape.py
   2. beidou_grid_processor.py
   3. build.py
   4. core\__init__.py
   5. core\aggregation.py
   6. core\coordinate_utils.py
   7. core\grid_calculator.py
   8. data\__init__.py
   9. data\data_validator.py
  10. data\raster_io.py
  11. data\vector_io.py
  12. gui\__init__.py
  13. gui\main_window.py
  14. gui\parameter_panel.py
  15. gui\progress_dialog.py
  16. gui\window_utils.py
  17. main.py
  18. test_basic.py
  19. test_center_and_eta.py
  20. test_completion_fix.py
  21. test_core.py
  22. test_detailed_progress.py
  23. test_gui.py
  24. test_improved_gui.py
  25. test_simple.py

================================================================================

================================================================================
文件 1: Script\beidou_transfrom_shape.py
================================================================================
# 文件编码: gbk

   1: #!/usr/bin/python2
   2: # -*- coding:cp936 -*-
   3: # author: AM
   4: # date: 2025/02/11
   5: '''
   6: 1、读取tif栅格数据，将tif栅格数据转换为numpy数组
   7: 2、遍历numpy数组，挑选局部最大值
   8: 3、返回局部最大值数组，转换为tif栅格数据
   9: '''
  10: 
  11: import arcpy
  12: from arcpy import env
  13: from arcpy.sa import *
  14: import os
  15: import numpy as np
  16: import math
  17: 
  18: 
  19: def local_max(array,level):
  20:     """
  21:     挑选数组局部最大值
  22:     :param array：二维数组；level: 网格数
  23:     :return: 局部最大值数组
  24:     """
  25:     rows, cols = array.shape
  26:     padded_array = np.pad(array, pad_width=2, mode='edge')  # 填充边界
  27:     result = np.zeros_like(array, dtype=float)
  28: 
  29:     for i in range(rows):
  30:         for j in range(cols):
  31:             # 提取5x5窗口
  32:             window = padded_array[i:i + int(level), j:j + int(level)]
  33:             # 找到窗口内的最大值
  34:             result[i, j] = np.max(window)
  35: 
  36:     return result
  37: 
  38: 
  39: def grid_size(level):
  40:     """
  41:     计算北斗网格的大小（经纬度差）
  42:     :param level: 北斗网格级别
  43:     :return: 经纬度差
  44:     """
  45:     level_grid = {1: [6, 4], 2: [30.0 / 60, 30.0 / 60], 3: [15.0 / 60, 10.0 / 60], 4: [1.0 / 60, 1.0 / 60],
  46:                   5: [4.0 / 3600, 4.0 / 3600],
  47:                   6: [2.0 / 3600, 2.0 / 3600], 7: [1.0 / 14400, 1.0 / 14400], 8: [1.0 / 115200, 1.0 / 115200],
  48:                   9: [1.0 / 921600, 1.0 / 921600], 10: [1.0 / 7372800, 1.0 / 7372800]}
  49: 
  50:     return level_grid[level]
  51: 
  52: 
  53: def generate_bdg_grid(grid_level, min_lon, min_lat, max_lon, max_lat, my_array):
  54:     """
  55:     生成二维北斗网格地图
  56:     :param grid_level: 北斗网格级别；min_lon: 最小经度；min_lat: 最小纬度；max_lon: 最大经度；max_lat: 最大纬度；my_array：原始数组
  57:     :return: 栅格数组
  58:     """
  59:     # 计算每个网格的经纬度范围
  60:     lon_size = grid_size(int(grid_level))[0]
  61:     lat_size = grid_size(int(grid_level))[1]
  62: 
  63:     # 计算行列数
  64:     cols = int(math.ceil((max_lon - min_lon) / lon_size))
  65:     # cols = int((max_lon - min_lon) / grid_size) + (1 if ((max_lon - min_lon) / grid_size) % 1 != 0 else 0)
  66:     rows = int(math.ceil((max_lat - min_lat) / lat_size))
  67: 
  68:     print(cols)
  69:     print(rows)
  70: 
  71:     # 初始化栅格数组
  72:     # grid = np.array([[0.0 for _ in range(cols)] for _ in range(rows)])
  73:     grid = np.zeros((rows, cols), dtype=float)
  74: 
  75:     # 计算每个局部区域的行数和列数
  76:     block_rows = my_array.shape[0] // grid.shape[0]
  77:     block_cols = my_array.shape[1] // grid.shape[1]
  78: 
  79:     # 填充栅格数组（示例：假设每个网格点有一个值）
  80:     # 这里可以根据实际需求填充数据
  81:     for i in range(grid.shape[0]):
  82:         for j in range(grid.shape[1]):
  83:             # 计算局部区域的起始和结束索引
  84:             start_row = i * block_rows
  85:             end_row = (i + 1) * block_rows
  86:             start_col = j * block_cols
  87:             end_col = (j + 1) * block_cols
  88: 
  89:             # 提取局部区域
  90:             block = my_array[start_row:end_row, start_col:end_col]
  91: 
  92:             # 计算局部区域的最大值
  93:             max_value = block.max()
  94: 
  95:             # 将最大值赋值给 array2
  96:             grid[i, j] = max_value
  97: 
  98:     remaining_rows = my_array.shape[0] % grid.shape[0]
  99:     remaining_cols = my_array.shape[1] % grid.shape[1]
 100: 
 101:     if remaining_rows > 0 or remaining_cols > 0:
 102:         for i in range(grid.shape[0]):
 103:             for j in range(grid.shape[1]):
 104:                 end_row = min((i + 1) * block_rows + remaining_rows, my_array.shape[0])
 105:                 end_col = min((j + 1) * block_cols + remaining_cols, my_array.shape[1])
 106:                 block = my_array[start_row:end_row, start_col:end_col]
 107:                 if block.size != 0:
 108:                     max_value = block.max()
 109:                     grid[i, j] = max_value
 110: 
 111:     return grid
 112: 
 113: 
 114: def calculate_grid_code(lat, lon, level):
 115:     """
 116:     计算经纬度坐标转换北斗二维网格位置码
 117:     :param lat：经度；lon：纬度；level: 北斗网格级别
 118:     :return: 北斗二维网格位置码
 119:     """
 120: 
 121:     # 第一级网格位置码计算，6°×4°
 122:     def first_level(lat, lon):
 123:         code1 = 'N' if lat >= 0 else 'S'  # 第一位码元
 124:         # ord()返回字符Unicode码点值，chr()返回码点值对应字符
 125:         code23 = str(int((lon + 180) / 6) + 1).zfill(2)  # 第二、三位码元
 126:         code4 = chr(ord('A') + int(abs(lat) / 4))  # 第四位码元
 127:         return code1 + code23 + code4
 128: 
 129:     # 第二级网格位置码计算，30′×30′，55.66km×55.66km
 130:     def second_level(lat, lon, first_code):
 131:         lon_base1 = int(lon / 6) * 6
 132:         lat_base1 = int(abs(lat) / 4) * 4
 133:         a2 = int((lon - lon_base1) * 2) + 1  # /0.5替换为*2，行号,lon
 134:         b2 = int((abs(lat) - lat_base1) * 2) + 1  # /0.5替换为*2，列号,lat
 135:         code5 = a2 - 1  # 第五位码元
 136:         code6_dict = {10: 'A', 11: 'B'}
 137:         code6 = b2 - 1  # 第六位码元
 138:         code6 = code6 if 0 <= code6 < 10 else code6_dict[code6]
 139:         second_list = [first_code + str(code5) + str(code6), lon_base1, lat_base1, a2, b2]
 140:         return second_list
 141: 
 142:     # 第三级网格位置码计算，15′×10′，27.83km×18.55km
 143:     def third_level(lat, lon, second_code):
 144:         lon_base2 = float(second_level(lat, lon, first_code)[1]) + float(
 145:                 (second_level(lat, lon, first_code)[3] - 1)) * 0.5
 146:         lat_base2 = float(second_level(lat, lon, first_code)[2]) + float(
 147:                 (second_level(lat, lon, first_code)[4] - 1)) * 0.5
 148:         a3 = int((lon - lon_base2) * 4) + 1  # /（15/60）替换为*4,行号，lon
 149:         b3 = int((abs(lat) - lat_base2) * 6) + 1  # /（10/60）替换为*6，列号，lat
 150:         # 创建一个 2x3 的数组，并按照 Z 字形填充 0-5
 151:         array3 = [
 152:             [0, 2, 4],
 153:             [1, 3, 5]
 154:         ]
 155:         lon_index3 = 1 if (lon - lon_base2) * 4 >= 1 else 0
 156:         if 0 < (abs(lat) - lat_base2) * 6 <= 1:
 157:             lat_index3 = 0
 158:         elif 1 < (abs(lat) - lat_base2) * 6 <= 2:
 159:             lat_index3 = 1
 160:         elif 2 < (abs(lat) - lat_base2) * 6 <= 3:
 161:             lat_index3 = 2
 162:         code7 = array3[lon_index3][lat_index3]  # 第七位码元
 163:         third_list = [second_code + str(code7), lon_base2, lat_base2, a3, b3]
 164:         return third_list
 165: 
 166:     # 此处示例a3=2，b3=6，但不影响第四级网格计算第三级网格的定位角点经纬度坐标
 167:     # 第四级网格位置码计算，1′×1′，1.85km×1.85km
 168:     def fourth_level(lat, lon, third_code):
 169:         lon_base3 = float(third_level(lat, lon, second_code)[1]) + float(
 170:                 (third_level(lat, lon, second_code)[3] - 1)) * 0.25
 171:         lat_base3 = float(third_level(lat, lon, second_code)[2]) + float(
 172:                 (third_level(lat, lon, second_code)[4] - 1)) * (1.0 / 6)
 173:         a4 = int((lon - lon_base3) * 60) + 1  # /(1.0/60)替换为*60，行号,lon
 174:         b4 = int((abs(lat) - lat_base3) * 60) + 1  # /(1.0/60)替换为*60，列号,lat
 175:         code8_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
 176:         code8 = a4 - 1  # 第八位码元
 177:         code8 = code8 if 0 <= code8 < 10 else code8_dict[code8]
 178:         code9 = b4 - 1  # 第九位码元
 179:         fourth_list = [third_code + str(code8) + str(code9), lon_base3, lat_base3, a4, b4]
 180:         return fourth_list
 181: 
 182:     # 第五级网格位置码计算，4″×4″，123.69m×123.69m
 183:     def fifth_level(lat, lon, fourth_code):
 184:         lon_base4 = float(fourth_level(lat, lon, third_code)[1]) + float(
 185:                 (fourth_level(lat, lon, third_code)[3] - 1)) * (1.0 / 60)
 186:         lat_base4 = float(fourth_level(lat, lon, third_code)[2]) + float(
 187:                 (fourth_level(lat, lon, third_code)[4] - 1)) * (1.0 / 60)
 188:         a5 = int((lon - lon_base4) * 900) + 1  # /(4.0/3600)替换为*900，行号,lon
 189:         b5 = int((abs(lat) - lat_base4) * 900) + 1  # /(4.0/3600)替换为*900，列号,lat
 190:         code10_11_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
 191:         code10 = a5 - 1  # 第十位码元
 192:         code10 = code10 if 0 <= code10 < 10 else code10_11_dict[code10]
 193:         code11 = b5 - 1  # 第十一位码元
 194:         code11 = code11 if 0 <= code11 < 10 else code10_11_dict[code11]
 195:         fifth_list = [fourth_code + str(code10) + str(code11), lon_base4, lat_base4, a5, b5]
 196:         return fifth_list
 197: 
 198:     # 第六级网格位置码计算，2″×2″，61.84m×61.84m
 199:     def sixth_level(lat, lon, second_code):
 200:         lon_base5 = float(fifth_level(lat, lon, fourth_code)[1]) + float(
 201:                 (fifth_level(lat, lon, fourth_code)[3] - 1)) * (1.0 / 900)
 202:         lat_base5 = float(fifth_level(lat, lon, fourth_code)[2]) + float(
 203:                 (fifth_level(lat, lon, fourth_code)[4] - 1)) * (1.0 / 900)
 204:         a6 = int((lon - lon_base5) * 1800) + 1  # /（2.0/3600）替换为*1800,行号，lon
 205:         b6 = int((abs(lat) - lat_base5) * 1800) + 1  # /（2.0/3600）替换为*1800，列号，lat
 206:         # 创建一个 2x2 的数组，并按照 Z 字形填充 0-3
 207:         array6 = [
 208:             [0, 2],
 209:             [1, 3]
 210:         ]
 211:         lon_index6 = 1 if (lon - lon_base5) * 1800 >= 1 else 0
 212:         lat_index6 = 1 if (lat - lat_base5) * 1800 >= 1 else 0
 213:         code12 = array6[lon_index6][lat_index6]  # 第十二位码元
 214:         sixth_list = [fifth_code + str(code12), lon_base5, lat_base5, a6, b6]
 215:         return sixth_list
 216: 
 217:     # 第七级网格位置码计算，（1/4）″×（1/4）″，7.73m×7.73m
 218:     def seventh_level(lat, lon, sixth_code):
 219:         lon_base6 = float(sixth_level(lat, lon, fifth_code)[1]) + float((sixth_level(lat, lon, fifth_code)[3] - 1)) * (
 220:             1.0 / 1800)
 221:         lat_base6 = float(sixth_level(lat, lon, fifth_code)[2]) + float((sixth_level(lat, lon, fifth_code)[4] - 1)) * (
 222:             1.0 / 1800)
 223:         a7 = int((lon - lon_base6) * 14400) + 1  # /（1.0/(4 * 3600)）替换为*14400，行号,lon
 224:         b7 = int((abs(lat) - lat_base6) * 14400) + 1  # /（1.0/(4 * 3600)）替换为*14400，列号,lat
 225:         code13 = a7 - 1  # 第十三位码元
 226:         code14 = b7 - 1  # 第十四位码元
 227:         seventh_list = [sixth_code + str(code13) + str(code14), lon_base6, lat_base6, a7, b7]
 228:         return seventh_list
 229: 
 230:     # 第八级网格位置码计算，（1/32）″×（1/32）″，0.97m×0.97m
 231:     def eighth_level(lat, lon, seventh_code):
 232:         lon_base7 = float(seventh_level(lat, lon, sixth_code)[1]) + float(
 233:                 (seventh_level(lat, lon, sixth_code)[3] - 1)) * (1.0 / 14400)
 234:         lat_base7 = float(seventh_level(lat, lon, sixth_code)[2]) + float(
 235:                 (seventh_level(lat, lon, sixth_code)[4] - 1)) * (1.0 / 14400)
 236:         a8 = int((lon - lon_base7) * 115200) + 1  # /（1.0/(32 * 3600)）替换为*115200，行号,lon
 237:         b8 = int((abs(lat) - lat_base7) * 115200) + 1  # /（1.0/(32 * 3600)）替换为*115200，列号,lat
 238:         code15 = a8 - 1  # 第十五位码元
 239:         code16 = b8 - 1  # 第十六位码元
 240:         eighth_list = [seventh_code + str(code15) + str(code16), lon_base7, lat_base7, a8, b8]
 241:         return eighth_list
 242: 
 243:     # 第九级网格位置码计算，（1/256）″×（1/256）″，12.0cm×12.0cm
 244:     def ninth_level(lat, lon, eighth_code):
 245:         lon_base8 = float(eighth_level(lat, lon, seventh_code)[1]) + float(
 246:                 (eighth_level(lat, lon, seventh_code)[3] - 1)) * (1.0 / 115200)
 247:         lat_base8 = float(eighth_level(lat, lon, seventh_code)[2]) + float(
 248:                 (eighth_level(lat, lon, seventh_code)[4] - 1)) * (1.0 / 115200)
 249:         a9 = int((lon - lon_base8) * 921600) + 1  # /（1.0/(256 * 3600)）替换为*921600，行号,lon
 250:         b9 = int((abs(lat) - lat_base8) * 921600) + 1  # /（1.0/(256 * 3600)）替换为*921600，列号,lat
 251:         code17 = a9 - 1  # 第十七位码元
 252:         code18 = b9 - 1  # 第十八位码元
 253:         ninth_list = [eighth_code + str(code17) + str(code18), lon_base8, lat_base8, a9, b9]
 254:         return ninth_list
 255: 
 256:     # 第十级网格位置码计算，（1/2048）″×（1/2048）″，1.5cm×1.5cm
 257:     def tenth_level(lat, lon, ninth_code):
 258:         lon_base9 = float(ninth_level(lat, lon, eighth_code)[1]) + float(
 259:                 (ninth_level(lat, lon, eighth_code)[3] - 1)) * (1.0 / 921600)
 260:         lat_base9 = float(ninth_level(lat, lon, eighth_code)[2]) + float(
 261:                 (ninth_level(lat, lon, eighth_code)[4] - 1)) * (1.0 / 921600)
 262:         a10 = int((lon - lon_base9) * 7372800) + 1  # /（1.0/(2048 * 3600)）替换为*7372800，行号,lon
 263:         b10 = int((abs(lat) - lat_base9) * 7372800) + 1  # /（1.0/(2048 * 3600)）替换为*7372800，列号,lat
 264:         code19 = a10 - 1  # 第十九位码元
 265:         code20 = b10 - 1  # 第二十位码元
 266:         return ninth_code + str(code19) + str(code20)
 267: 
 268:     first_code = first_level(lat, lon)
 269:     second_code = second_level(lat, lon, first_code)[0]
 270:     third_code = third_level(lat, lon, second_code)[0]
 271:     fourth_code = fourth_level(lat, lon, third_code)[0]
 272:     fifth_code = fifth_level(lat, lon, fourth_code)[0]
 273:     sixth_code = sixth_level(lat, lon, fifth_code)[0]
 274:     seventh_code = seventh_level(lat, lon, sixth_code)[0]
 275:     eighth_code = eighth_level(lat, lon, seventh_code)[0]
 276:     ninth_code = ninth_level(lat, lon, eighth_code)[0]
 277:     tenth_code = tenth_level(lat, lon, ninth_code)
 278: 
 279:     grid_codes_dict = {1: first_code, 2: second_code, 3: third_code, 4: fourth_code, 5: fifth_code, 6: sixth_code,
 280:                        7: seventh_code, 8: eighth_code, 9: ninth_code, 10: tenth_code}
 281: 
 282:     return grid_codes_dict[level]
 283: 
 284: 
 285: 
 286: raster = arcpy.GetParameterAsText(0)  # raster = ur'E:\WORK\weiting_dsm_CGCS2000\test.tif'
 287: outputraster = arcpy.GetParameterAsText(1)  # outputraster = ur'test_0709.tif'
 288: level = arcpy.GetParameterAsText(2)
 289: outpolygon = outputraster.replace('.tif','.shp')
 290: 
 291: spatial_ref = arcpy.Describe(raster).spatialReference.name
 292: x_cell_size = arcpy.Describe(raster).meanCellWidth
 293: y_cell_size = arcpy.Describe(raster).meanCellHeight
 294: extent = arcpy.Describe(raster).extent
 295: lowerLeft = extent.lowerLeft  # 读取不到
 296: 
 297: # 注意此处extent需转换为str字符串格式，否则无法输出
 298: min_lon = float(str(extent).split()[0])  # 最小经度
 299: min_lat = float(str(extent).split()[1])  # 最小纬度
 300: max_lon = float(str(extent).split()[2])  # 最大经度
 301: max_lat = float(str(extent).split()[3])  # 最大纬度
 302: 
 303: # 将栅格转换为 NumPy 数组
 304: my_array = arcpy.RasterToNumPyArray(raster, nodata_to_value=0)  # 需设置NoData值，否则为默认tif数据属性
 305: # arcpy.AddMessage(type(my_array))
 306: # my_array = np.random.randint(-3, 100, (10, 10))
 307: # arcpy.AddMessage(my_array)
 308: arcpy.AddMessage(u"----------------原始转换数组----------------")
 309: arcpy.AddMessage(np.max(my_array))
 310: arcpy.AddMessage(np.min(my_array))
 311: 
 312: rows, cols = my_array.shape
 313: arcpy.AddMessage(rows)
 314: arcpy.AddMessage(cols)
 315: # arcpy.AddMessage(u"----------------局部最大值----------------")
 316: # 调用函数并输出结果
 317: local_max_result = local_max(my_array, level)
 318: # # arcpy.AddMessage(local_max_result)
 319: # arcpy.AddMessage(np.max(local_max_result))
 320: # arcpy.AddMessage(np.min(local_max_result))
 321: #
 322: # local_max_rows, local_max_cols = local_max_result.shape
 323: # arcpy.AddMessage(local_max_rows)
 324: # arcpy.AddMessage(local_max_cols)
 325: arcpy.AddMessage(u"----------------北斗网格----------------")
 326: bdg_grid = generate_bdg_grid(level, min_lon, min_lat, max_lon, max_lat, local_max_result)
 327: arcpy.AddMessage(bdg_grid)
 328: arcpy.AddMessage(np.max(bdg_grid))
 329: arcpy.AddMessage(np.min(bdg_grid))
 330: bdg_grid_rows, bdg_grid_cols = bdg_grid.shape
 331: arcpy.AddMessage(bdg_grid_rows)
 332: arcpy.AddMessage(bdg_grid_cols)
 333: 
 334: # bdg_grid = np.floor(bdg_grid)  # 使用floor取整
 335: 
 336: # 将 NumPy 数组 转换为 栅格
 337: # new_raster = arcpy.NumPyArrayToRaster(bdg_grid)
 338: outputraster_path = os.path.join(os.path.dirname(os.path.abspath(raster)), outputraster)
 339: # arcpy.AddMessage(outputraster_path)
 340: # 注意北斗网格转换前后行列的大小需要对应，否则出现偏移及等比例缩小
 341: new_raster = arcpy.NumPyArrayToRaster(bdg_grid, arcpy.Point(min_lon, min_lat), x_cell_size=grid_size(int(level))[0],
 342:                                       y_cell_size=grid_size(int(level))[1], value_to_nodata=0)  # 需设置NoData值，否则为默认tif数据属性
 343: new_raster.save(outputraster_path)
 344: 
 345: # 转为整型
 346: int_raster = Int(outputraster_path)
 347: int_outputraster_path = os.path.join(os.path.dirname(os.path.abspath(raster)), "int_" + outputraster)
 348: int_raster.save(int_outputraster_path)
 349: 
 350: shp_path = os.path.join(os.path.dirname(os.path.abspath(raster)), "int_" + outpolygon)
 351: # 不简化
 352: arcpy.RasterToPolygon_conversion(int_outputraster_path, shp_path, "NO_SIMPLIFY")
 353: 


================================================================================
文件 2: beidou_grid_processor.py
================================================================================

   1: """
   2: 北斗网格转换处理器
   3: 整合所有功能的主处理类
   4: """
   5: 
   6: import os
   7: import numpy as np
   8: import logging
   9: from typing import Optional, Dict, Any, Tuple, Callable
  10: from dataclasses import dataclass
  11: 
  12: from core import BeidouGridCalculator, AggregationMethod, DataAggregator
  13: from data.raster_io import RasterReader, RasterWriter
  14: from data.vector_io import VectorWriter
  15: from data.data_validator import DataValidator
  16: 
  17: 
  18: @dataclass
  19: class ProcessingConfig:
  20:     """处理配置"""
  21:     # 输入输出
  22:     input_file: str
  23:     output_raster: Optional[str] = None
  24:     output_vector: Optional[str] = None
  25:     output_integer_raster: bool = True  # 是否同时输出整型栅格文件
  26:     
  27:     # 网格参数
  28:     grid_level: int = 7
  29:     
  30:     # 聚合参数
  31:     aggregation_method: str = "max"
  32:     window_size: int = 5
  33:     boundary_handling: str = "edge"
  34:     
  35:     # 输出参数
  36:     output_dtype: str = "float32"
  37:     compression: str = "lzw"
  38:     create_overview: bool = True
  39:     
  40:     # 处理参数
  41:     chunk_size: int = 1024
  42:     parallel_processing: bool = False
  43:     max_memory_gb: float = 4.0
  44:     
  45:     # 其他选项
  46:     nodata_value: Optional[float] = None
  47:     preserve_crs: bool = True
  48:     verbose: bool = True
  49: 
  50: 
  51: class BeidouGridProcessor:
  52:     """北斗网格转换处理器"""
  53:     
  54:     def __init__(self, config: ProcessingConfig):
  55:         """
  56:         初始化处理器
  57:         
  58:         Args:
  59:             config: 处理配置
  60:         """
  61:         self.config = config
  62:         self.grid_calculator = BeidouGridCalculator()
  63:         self.aggregator = None
  64:         self.progress_callback: Optional[Callable] = None
  65:         self.is_cancelled = False
  66:         self.temp_files = []  # 记录临时文件
  67:         
  68:         # 设置日志
  69:         if config.verbose:
  70:             logging.basicConfig(level=logging.INFO)
  71:         
  72:         # 验证配置
  73:         self._validate_config()
  74:     
  75:     def set_progress_callback(self, callback: Callable[[int, str, str, dict], None]):
  76:         """
  77:         设置进度回调函数
  78: 
  79:         Args:
  80:             callback: 回调函数，参数为 (progress_percent, message, detail, extra_info)
  81:         """
  82:         self.progress_callback = callback
  83: 
  84:     def _update_progress(self, percent: int, message: str, detail: str = "", extra_info: dict = None):
  85:         """更新进度"""
  86:         if self.progress_callback:
  87:             if extra_info:
  88:                 self.progress_callback(percent, message, detail, extra_info)
  89:             else:
  90:                 # 为了向后兼容，如果没有额外信息，仍然使用3参数调用
  91:                 try:
  92:                     self.progress_callback(percent, message, detail, {})
  93:                 except TypeError:
  94:                     # 如果回调函数只接受3个参数，则使用旧方式
  95:                     self.progress_callback(percent, message, detail)
  96:         elif self.config.verbose:
  97:             logging.info(f"进度 {percent}%: {message} {detail}")
  98: 
  99:     def cancel(self):
 100:         """取消处理"""
 101:         self.is_cancelled = True
 102:         self._cleanup_temp_files()
 103:         logging.info("处理已被用户取消")
 104: 
 105:     def _check_cancelled(self):
 106:         """检查是否已取消"""
 107:         if self.is_cancelled:
 108:             raise InterruptedError("处理已被用户取消")
 109: 
 110:     def _cleanup_temp_files(self):
 111:         """清理临时文件"""
 112:         import os
 113:         for temp_file in self.temp_files:
 114:             try:
 115:                 if os.path.exists(temp_file):
 116:                     os.remove(temp_file)
 117:                     logging.info(f"已删除临时文件: {temp_file}")
 118:             except Exception as e:
 119:                 logging.warning(f"删除临时文件失败 {temp_file}: {e}")
 120:         self.temp_files.clear()
 121: 
 122:     def _get_file_size(self, file_path: str) -> str:
 123:         """获取文件大小的可读格式"""
 124:         import os
 125:         try:
 126:             if os.path.exists(file_path):
 127:                 size_bytes = os.path.getsize(file_path)
 128:                 if size_bytes < 1024:
 129:                     return f"{size_bytes} B"
 130:                 elif size_bytes < 1024 * 1024:
 131:                     return f"{size_bytes / 1024:.1f} KB"
 132:                 elif size_bytes < 1024 * 1024 * 1024:
 133:                     return f"{size_bytes / (1024 * 1024):.1f} MB"
 134:                 else:
 135:                     return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
 136:             return "未知大小"
 137:         except Exception:
 138:             return "未知大小"
 139:     
 140:     def _validate_config(self):
 141:         """验证配置"""
 142:         params = {
 143:             'input_file': self.config.input_file,
 144:             'output_file': self.config.output_raster or self.config.output_vector or 'temp.tif',
 145:             'grid_level': self.config.grid_level,
 146:             'aggregation_method': self.config.aggregation_method,
 147:             'window_size': self.config.window_size
 148:         }
 149:         
 150:         is_valid, errors = DataValidator.validate_processing_parameters(params)
 151:         if not is_valid:
 152:             raise ValueError(f"配置验证失败: {'; '.join(errors)}")
 153:     
 154:     def process(self) -> Dict[str, Any]:
 155:         """
 156:         执行完整的处理流程
 157:         
 158:         Returns:
 159:             处理结果信息
 160:         """
 161:         try:
 162:             self._update_progress(0, "开始处理", f"输入文件: {self.config.input_file}")
 163:             self._check_cancelled()
 164: 
 165:             # 1. 读取输入数据
 166:             self._update_progress(10, "读取输入数据", "正在加载栅格文件...")
 167:             input_data = self._read_input_data()
 168:             self._check_cancelled()
 169: 
 170:             # 2. 数据预处理
 171:             self._update_progress(20, "数据预处理", f"数据尺寸: {input_data['array'].shape}")
 172:             processed_data = self._preprocess_data(input_data)
 173:             self._check_cancelled()
 174: 
 175:             # 3. 聚合处理
 176:             self._update_progress(40, "执行数据聚合", f"聚合方法: {self.config.aggregation_method}, 窗口大小: {self.config.window_size}")
 177:             aggregated_data = self._aggregate_data(processed_data)
 178:             self._check_cancelled()
 179: 
 180:             # 4. 生成网格
 181:             self._update_progress(60, "生成北斗网格", f"网格级别: {self.config.grid_level}")
 182:             grid_data = self._generate_grid(aggregated_data)
 183:             self._check_cancelled()
 184: 
 185:             # 5. 输出结果
 186:             self._update_progress(80, "输出结果", "正在写入输出文件...")
 187:             output_info = self._write_outputs(grid_data)
 188:             self._check_cancelled()
 189: 
 190:             self._update_progress(100, "处理完成", f"输出网格数量: {np.count_nonzero(~np.isnan(grid_data))}")
 191: 
 192:             # 处理完成后稍等一下让用户看到完成信息
 193:             import time
 194:             time.sleep(1.5)
 195: 
 196:             return {
 197:                 'success': True,
 198:                 'input_shape': input_data['array'].shape,
 199:                 'output_shape': grid_data.shape,
 200:                 'grid_level': self.config.grid_level,
 201:                 'aggregation_method': self.config.aggregation_method,
 202:                 'output_files': output_info,
 203:                 'statistics': self._calculate_statistics(grid_data)
 204:             }
 205:             
 206:         except Exception as e:
 207:             logging.error(f"处理失败: {str(e)}")
 208:             return {
 209:                 'success': False,
 210:                 'error': str(e)
 211:             }
 212:     
 213:     def _read_input_data(self) -> Dict[str, Any]:
 214:         """读取输入数据"""
 215:         with RasterReader(self.config.input_file) as reader:
 216:             array = reader.read_array(masked=False)
 217:             bounds = reader.get_bounds()
 218:             transform = reader.get_transform()
 219:             crs = reader.get_crs()
 220:             nodata = reader.get_nodata_value()
 221:             
 222:             # 处理掩码数组
 223:             if hasattr(array, 'mask'):
 224:                 array = array.filled(nodata or np.nan)
 225:             
 226:             return {
 227:                 'array': array,
 228:                 'bounds': bounds,
 229:                 'transform': transform,
 230:                 'crs': crs,
 231:                 'nodata': nodata or self.config.nodata_value
 232:             }
 233:     
 234:     def _preprocess_data(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
 235:         """数据预处理"""
 236:         array = input_data['array']
 237:         nodata = input_data['nodata']
 238:         
 239:         # 验证数组
 240:         if not DataValidator.validate_array(array):
 241:             raise ValueError("输入数组验证失败")
 242:         
 243:         # 验证内存使用
 244:         if not DataValidator.validate_memory_usage(array.shape, array.dtype, self.config.max_memory_gb):
 245:             logging.warning("内存使用量可能过大，建议分块处理")
 246:         
 247:         # 处理无效值
 248:         if nodata is not None:
 249:             array = np.where(array == nodata, np.nan, array)
 250:         
 251:         input_data['array'] = array
 252:         return input_data
 253:     
 254:     def _aggregate_data(self, input_data: Dict[str, Any]) -> np.ndarray:
 255:         """数据聚合"""
 256:         array = input_data['array']
 257:         
 258:         # 创建聚合器
 259:         self.aggregator = DataAggregator(
 260:             method=self.config.aggregation_method,
 261:             window_size=self.config.window_size,
 262:             boundary_handling=self.config.boundary_handling
 263:         )
 264:         
 265:         # 执行聚合
 266:         if self.config.parallel_processing:
 267:             # TODO: 实现并行处理
 268:             aggregated = self.aggregator.aggregate(array, self.config.nodata_value, self._update_progress, self._check_cancelled)
 269:         else:
 270:             aggregated = self.aggregator.aggregate(array, self.config.nodata_value, self._update_progress, self._check_cancelled)
 271:         
 272:         return aggregated
 273:     
 274:     def _generate_grid(self, aggregated_data: np.ndarray) -> np.ndarray:
 275:         """生成北斗网格"""
 276:         # 获取边界信息
 277:         bounds = self._get_processing_bounds()
 278:         min_lon, min_lat, max_lon, max_lat = bounds
 279: 
 280:         self._update_progress(62, "计算网格维度", f"边界范围: ({min_lon:.4f}, {min_lat:.4f}) - ({max_lon:.4f}, {max_lat:.4f})")
 281: 
 282:         # 计算网格维度
 283:         rows, cols = self.grid_calculator.calculate_grid_dimensions(
 284:             min_lon, min_lat, max_lon, max_lat, self.config.grid_level
 285:         )
 286: 
 287:         self._update_progress(65, "重采样数据", f"目标网格尺寸: {rows} x {cols} = {rows*cols:,} 个网格")
 288: 
 289:         # 生成网格数据
 290:         grid_data = self._resample_to_grid(aggregated_data, (rows, cols))
 291: 
 292:         return grid_data
 293:     
 294:     def _resample_to_grid(self, data: np.ndarray, target_shape: Tuple[int, int]) -> np.ndarray:
 295:         """重采样到目标网格"""
 296:         from scipy.ndimage import zoom
 297:         
 298:         # 计算缩放因子
 299:         zoom_factors = (target_shape[0] / data.shape[0], target_shape[1] / data.shape[1])
 300:         
 301:         # 重采样
 302:         if self.config.aggregation_method == "max":
 303:             # 对于最大值，使用最近邻插值
 304:             resampled = zoom(data, zoom_factors, order=0)
 305:         else:
 306:             # 对于其他方法，使用双线性插值
 307:             resampled = zoom(data, zoom_factors, order=1)
 308:         
 309:         return resampled
 310:     
 311:     def _write_outputs(self, grid_data: np.ndarray) -> Dict[str, str]:
 312:         """写入输出文件"""
 313:         output_files = {}
 314:         bounds = self._get_processing_bounds()
 315:         
 316:         # 写入栅格文件
 317:         if self.config.output_raster:
 318:             self._update_progress(82, "写入栅格文件", f"输出文件: {self.config.output_raster}")
 319:             # 记录输出文件以便取消时清理
 320:             self.temp_files.append(self.config.output_raster)
 321:             success = self._write_raster_output(grid_data, bounds)
 322:             if success:
 323:                 output_files['raster'] = self.config.output_raster
 324:                 # 成功写入后从临时文件列表中移除
 325:                 if self.config.output_raster in self.temp_files:
 326:                     self.temp_files.remove(self.config.output_raster)
 327:                 self._update_progress(85, "浮点栅格文件写入完成", f"文件大小: {self._get_file_size(self.config.output_raster)}")
 328: 
 329:                 # 如果需要，同时生成整型栅格文件
 330:                 if self.config.output_integer_raster:
 331:                     int_raster_path = self._get_integer_raster_path(self.config.output_raster)
 332:                     self._update_progress(86, "写入整型栅格文件", f"输出文件: {int_raster_path}")
 333:                     self.temp_files.append(int_raster_path)
 334:                     int_success = self._write_integer_raster_output(grid_data, bounds, int_raster_path)
 335:                     if int_success:
 336:                         output_files['integer_raster'] = int_raster_path
 337:                         if int_raster_path in self.temp_files:
 338:                             self.temp_files.remove(int_raster_path)
 339:                         self._update_progress(87, "整型栅格文件写入完成", f"文件大小: {self._get_file_size(int_raster_path)}")
 340: 
 341:         # 写入矢量文件
 342:         if self.config.output_vector:
 343:             self._update_progress(90, "写入矢量文件", f"输出文件: {self.config.output_vector}")
 344:             # 记录输出文件以便取消时清理
 345:             self.temp_files.append(self.config.output_vector)
 346: 
 347:             # 如果有整型栅格文件，优先使用整型数据生成矢量（更高效且符合原始ArcPy逻辑）
 348:             vector_data = grid_data
 349:             if self.config.output_integer_raster and 'integer_raster' in output_files:
 350:                 # 使用整型数据
 351:                 vector_data = np.where(np.isnan(grid_data), 0, np.floor(grid_data)).astype(np.int32)
 352: 
 353:             success = self._write_vector_output(vector_data, bounds)
 354:             if success:
 355:                 output_files['vector'] = self.config.output_vector
 356:                 # 成功写入后从临时文件列表中移除
 357:                 if self.config.output_vector in self.temp_files:
 358:                     self.temp_files.remove(self.config.output_vector)
 359:                 self._update_progress(95, "矢量文件写入完成", f"文件大小: {self._get_file_size(self.config.output_vector)}")
 360:         
 361:         return output_files
 362:     
 363:     def _write_raster_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float]) -> bool:
 364:         """写入栅格输出"""
 365:         min_lon, min_lat, max_lon, max_lat = bounds
 366:         height, width = grid_data.shape
 367:         
 368:         # 创建仿射变换
 369:         transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)
 370:         
 371:         # 获取CRS
 372:         crs = self._get_output_crs()
 373:         
 374:         # 写入文件
 375:         writer = RasterWriter(
 376:             self.config.output_raster,
 377:             grid_data,
 378:             transform,
 379:             crs=crs,
 380:             nodata=self.config.nodata_value,
 381:             dtype=self.config.output_dtype,
 382:             compress=self.config.compression
 383:         )
 384:         
 385:         return writer.write(self.config.create_overview)
 386: 
 387:     def _get_integer_raster_path(self, raster_path: str) -> str:
 388:         """获取整型栅格文件路径"""
 389:         import os
 390:         dir_name = os.path.dirname(raster_path)
 391:         base_name = os.path.basename(raster_path)
 392:         name, ext = os.path.splitext(base_name)
 393:         return os.path.join(dir_name, f"int_{name}{ext}")
 394: 
 395:     def _write_integer_raster_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float], output_path: str) -> bool:
 396:         """写入整型栅格输出"""
 397:         min_lon, min_lat, max_lon, max_lat = bounds
 398:         height, width = grid_data.shape
 399: 
 400:         # 将浮点数据转换为整数（类似ArcPy的Int()函数）
 401:         # 处理NaN值，将其设为0
 402:         int_grid_data = np.where(np.isnan(grid_data), 0, np.floor(grid_data)).astype(np.int32)
 403: 
 404:         # 创建仿射变换
 405:         transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)
 406: 
 407:         # 获取CRS
 408:         crs = self._get_output_crs()
 409: 
 410:         # 写入文件
 411:         writer = RasterWriter(
 412:             output_path,
 413:             int_grid_data,
 414:             transform,
 415:             crs=crs,
 416:             nodata=0,  # 整型文件使用0作为无效值
 417:             dtype='int32',
 418:             compress=self.config.compression
 419:         )
 420: 
 421:         return writer.write(self.config.create_overview)
 422:     
 423:     def _write_vector_output(self, grid_data: np.ndarray, bounds: Tuple[float, float, float, float]) -> bool:
 424:         """写入矢量输出"""
 425:         min_lon, min_lat, max_lon, max_lat = bounds
 426:         height, width = grid_data.shape
 427:         crs = self._get_output_crs()
 428: 
 429:         # 创建仿射变换
 430:         from data.raster_io import RasterWriter
 431:         transform = RasterWriter.create_from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)
 432: 
 433:         writer = VectorWriter(self.config.output_vector, crs)
 434: 
 435:         # 使用raster_to_polygons方法提取实际形状，而不是生成正方形网格
 436:         return writer.raster_to_polygons(
 437:             grid_data,
 438:             transform,
 439:             mask_value=0,  # 将0值作为掩码值
 440:             progress_callback=self._update_progress
 441:         )
 442:     
 443:     def _get_processing_bounds(self) -> Tuple[float, float, float, float]:
 444:         """获取处理边界"""
 445:         # 这里应该从输入数据获取边界
 446:         # 简化实现，实际应该从 _read_input_data 的结果获取
 447:         with RasterReader(self.config.input_file) as reader:
 448:             return reader.get_bounds()
 449:     
 450:     def _get_output_crs(self):
 451:         """获取输出坐标系"""
 452:         if self.config.preserve_crs:
 453:             with RasterReader(self.config.input_file) as reader:
 454:                 return reader.get_crs()
 455:         return None
 456:     
 457:     def _calculate_statistics(self, grid_data: np.ndarray) -> Dict[str, float]:
 458:         """计算统计信息"""
 459:         valid_data = grid_data[~np.isnan(grid_data)]
 460:         
 461:         if valid_data.size == 0:
 462:             return {'count': 0}
 463:         
 464:         return {
 465:             'count': int(valid_data.size),
 466:             'min': float(np.min(valid_data)),
 467:             'max': float(np.max(valid_data)),
 468:             'mean': float(np.mean(valid_data)),
 469:             'std': float(np.std(valid_data))
 470:         }
 471: 


================================================================================
文件 3: build.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 北斗网格转换工具打包脚本
   4: 使用PyInstaller将Python项目打包为exe文件
   5: 
   6: 使用方法:
   7: 1. 安装依赖: pip install -r requirements.txt
   8: 2. 运行打包: python build.py
   9: 3. 或者指定选项: python build.py --onefile --console
  10: 
  11: 作者: 北斗网格转换工具开发团队
  12: 版本: 2.0
  13: """
  14: 
  15: import os
  16: import sys
  17: import shutil
  18: import subprocess
  19: import argparse
  20: from pathlib import Path
  21: 
  22: 
  23: class BeidouGridBuilder:
  24:     """北斗网格转换工具打包器"""
  25:     
  26:     def __init__(self):
  27:         self.project_root = Path(__file__).parent.absolute()
  28:         self.dist_dir = self.project_root / "dist"
  29:         self.build_dir = self.project_root / "build"
  30:         self.spec_file = self.project_root / "main.spec"
  31:         
  32:     def clean_build(self):
  33:         """清理之前的构建文件"""
  34:         print("清理构建目录...")
  35:         
  36:         # 删除构建目录
  37:         if self.build_dir.exists():
  38:             shutil.rmtree(self.build_dir)
  39:             print(f"已删除: {self.build_dir}")
  40:             
  41:         if self.dist_dir.exists():
  42:             shutil.rmtree(self.dist_dir)
  43:             print(f"已删除: {self.dist_dir}")
  44:             
  45:         # 删除spec文件
  46:         if self.spec_file.exists():
  47:             self.spec_file.unlink()
  48:             print(f"已删除: {self.spec_file}")
  49:             
  50:         print("清理完成!")
  51:     
  52:     def check_dependencies(self):
  53:         """检查依赖是否安装"""
  54:         print("检查依赖...")
  55: 
  56:         required_packages = [
  57:             'rasterio',
  58:             'fiona',
  59:             'shapely',
  60:             'numpy',
  61:             'scipy',
  62:             'pyproj'
  63:         ]
  64: 
  65:         missing_packages = []
  66: 
  67:         # 检查PyInstaller
  68:         try:
  69:             import PyInstaller
  70:             print("✓ pyinstaller")
  71:         except ImportError:
  72:             try:
  73:                 import pyinstaller
  74:                 print("✓ pyinstaller")
  75:             except ImportError:
  76:                 # 尝试通过subprocess检查
  77:                 try:
  78:                     result = subprocess.run(['pyinstaller', '--version'],
  79:                                           capture_output=True, text=True, check=True)
  80:                     print("✓ pyinstaller")
  81:                 except (subprocess.CalledProcessError, FileNotFoundError):
  82:                     missing_packages.append('pyinstaller')
  83:                     print("✗ pyinstaller (缺失)")
  84: 
  85:         # 检查其他包
  86:         for package in required_packages:
  87:             try:
  88:                 __import__(package)
  89:                 print(f"✓ {package}")
  90:             except ImportError:
  91:                 missing_packages.append(package)
  92:                 print(f"✗ {package} (缺失)")
  93: 
  94:         if missing_packages:
  95:             print(f"\n错误: 缺少以下依赖包: {', '.join(missing_packages)}")
  96:             print("请运行: pip install -r requirements.txt")
  97:             return False
  98: 
  99:         print("依赖检查通过!")
 100:         return True
 101:     
 102:     def get_hidden_imports(self):
 103:         """获取隐藏导入列表"""
 104:         return [
 105:             # 地理空间库
 106:             'rasterio',
 107:             'rasterio.drivers',
 108:             'rasterio._shim',
 109:             'rasterio.control',
 110:             'rasterio.crs',
 111:             'rasterio.dtypes',
 112:             'rasterio.env',
 113:             'rasterio.errors',
 114:             'rasterio.features',
 115:             'rasterio.fill',
 116:             'rasterio.mask',
 117:             'rasterio.merge',
 118:             'rasterio.plot',
 119:             'rasterio.profiles',
 120:             'rasterio.rio',
 121:             'rasterio.sample',
 122:             'rasterio.transform',
 123:             'rasterio.warp',
 124:             'rasterio.windows',
 125:             'rasterio.vrt',
 126:             'rasterio._io',
 127:             'rasterio._base',
 128:             'rasterio._version',
 129:             'rasterio.coords',
 130:             'rasterio.enums',
 131:             'rasterio.path',
 132:             'rasterio.session',
 133:             'rasterio.shutil',
 134:             'rasterio.tools',
 135:             'rasterio.tools.mask',
 136:             'rasterio.tools.merge',
 137:             'rasterio.warp',
 138:             'fiona',
 139:             'fiona.drivers',
 140:             'fiona.crs',
 141:             'fiona.transform',
 142:             'shapely',
 143:             'shapely.geometry',
 144:             'shapely.ops',
 145:             'shapely.prepared',
 146:             'pyproj',
 147:             'pyproj.crs',
 148:             'pyproj.transformer',
 149:             
 150:             # 科学计算库
 151:             'numpy',
 152:             'scipy',
 153:             'scipy.ndimage',
 154:             'scipy.spatial',
 155:             'scipy.interpolate',
 156:             'scipy.stats',
 157:             'scipy.sparse',
 158:             'scipy.sparse.csgraph',
 159:             'scipy.sparse.linalg',
 160:             'scipy.linalg',
 161:             'scipy.optimize',
 162:             'scipy.integrate',
 163:             'scipy.special',
 164:             'scipy._lib',
 165:             'scipy._lib._util',
 166:             'scipy._cyutility',
 167:             'scipy.sparse._matrix',
 168:             'scipy.sparse._base',
 169:             'scipy.sparse._csparsetools',
 170:             'scipy.sparse._sparsetools',
 171:             
 172:             # GUI相关
 173:             'tkinter',
 174:             'tkinter.ttk',
 175:             'tkinter.filedialog',
 176:             'tkinter.messagebox',
 177:             
 178:             # 项目模块
 179:             'core',
 180:             'core.aggregation',
 181:             'core.coordinate_utils', 
 182:             'core.grid_calculator',
 183:             'data',
 184:             'data.data_validator',
 185:             'data.raster_io',
 186:             'data.vector_io',
 187:             'gui',
 188:             'gui.main_window',
 189:             'gui.parameter_panel',
 190:             'gui.progress_dialog',
 191:             'gui.window_utils',
 192:         ]
 193:     
 194:     def get_data_files(self):
 195:         """获取需要包含的数据文件"""
 196:         data_files = []
 197: 
 198:         # 不包含测试数据和文档文件，保持exe文件精简
 199: 
 200:         return data_files
 201:     
 202:     def build_exe(self, onefile=True, console=False, debug=False):
 203:         """构建exe文件"""
 204:         print("开始构建exe文件...")
 205:         
 206:         # 构建PyInstaller命令
 207:         cmd = [
 208:             sys.executable, "-m", "PyInstaller",
 209:             "--name=北斗网格转换工具",
 210:             "--clean",
 211:             "--noconfirm",
 212:         ]
 213:         
 214:         # 添加选项
 215:         if onefile:
 216:             cmd.append("--onefile")
 217:         else:
 218:             cmd.append("--onedir")
 219:             
 220:         if not console:
 221:             cmd.append("--windowed")
 222:         else:
 223:             cmd.append("--console")
 224:             
 225:         if debug:
 226:             cmd.append("--debug=all")
 227:         
 228:         # 添加隐藏导入
 229:         for import_name in self.get_hidden_imports():
 230:             cmd.extend(["--hidden-import", import_name])
 231:         
 232:         # 添加数据文件
 233:         for src, dst in self.get_data_files():
 234:             cmd.extend(["--add-data", f"{src};{dst}"])
 235:         
 236:         # 添加图标（如果存在）
 237:         icon_path = self.project_root / "icon.ico"
 238:         if icon_path.exists():
 239:             cmd.extend(["--icon", str(icon_path)])
 240:         
 241:         # 排除不需要的模块
 242:         exclude_modules = [
 243:             "matplotlib",
 244:             "IPython",
 245:             "jupyter",
 246:             "notebook",
 247:             "pandas",
 248:             "seaborn",
 249:             "plotly"
 250:         ]
 251:         
 252:         for module in exclude_modules:
 253:             cmd.extend(["--exclude-module", module])
 254:         
 255:         # 添加主文件
 256:         cmd.append("main.py")
 257:         
 258:         print(f"执行命令: {' '.join(cmd)}")
 259:         
 260:         # 执行构建
 261:         try:
 262:             print("开始执行PyInstaller...")
 263:             print("=" * 60)
 264: 
 265:             # 实时显示输出
 266:             process = subprocess.Popen(cmd, cwd=self.project_root,
 267:                                      stdout=subprocess.PIPE,
 268:                                      stderr=subprocess.STDOUT,
 269:                                      text=True, bufsize=1, universal_newlines=True)
 270: 
 271:             # 实时打印输出
 272:             while True:
 273:                 output = process.stdout.readline()
 274:                 if output == '' and process.poll() is not None:
 275:                     break
 276:                 if output:
 277:                     print(output.strip())
 278: 
 279:             # 等待进程完成
 280:             return_code = process.poll()
 281: 
 282:             if return_code == 0:
 283:                 print("=" * 60)
 284:                 print("构建成功!")
 285:                 return True
 286:             else:
 287:                 print("=" * 60)
 288:                 print(f"构建失败，返回码: {return_code}")
 289:                 return False
 290: 
 291:         except Exception as e:
 292:             print(f"构建过程中发生错误: {e}")
 293:             return False
 294:     
 295:     def post_build_cleanup(self):
 296:         """构建后清理"""
 297:         print("执行构建后清理...")
 298:         
 299:         # 删除不需要的文件
 300:         cleanup_patterns = [
 301:             "*.pyc",
 302:             "__pycache__",
 303:             "*.pyo",
 304:             "*.pyd"
 305:         ]
 306:         
 307:         if self.dist_dir.exists():
 308:             for pattern in cleanup_patterns:
 309:                 for file_path in self.dist_dir.rglob(pattern):
 310:                     if file_path.is_file():
 311:                         file_path.unlink()
 312:                         print(f"删除: {file_path}")
 313:                     elif file_path.is_dir():
 314:                         shutil.rmtree(file_path)
 315:                         print(f"删除目录: {file_path}")
 316:         
 317:         print("清理完成!")
 318:     
 319:     def create_installer_info(self):
 320:         """创建安装说明文件"""
 321:         info_content = """
 322: 北斗网格转换工具 v2.0 - 安装说明
 323: 
 324: 1. 系统要求:
 325:    - Windows 7/8/10/11 (64位)
 326:    - 至少 4GB 内存
 327:    - 至少 1GB 可用磁盘空间
 328: 
 329: 2. 安装步骤:
 330:    - 将exe文件复制到任意目录
 331:    - 双击运行即可
 332: 
 333: 3. 使用说明:
 334:    - GUI模式: 直接双击exe文件
 335:    - 命令行模式: 在命令提示符中运行 "北斗网格转换工具.exe --cli --help"
 336: 
 337: 4. 注意事项:
 338:    - 首次运行可能需要较长时间初始化
 339:    - 建议将exe文件放在英文路径下
 340:    - 如遇到问题，请查看操作手册
 341: 
 342: 5. 技术支持:
 343:    - 详细操作手册: 北斗网格工具操作手册.docx
 344:    - 测试数据: 测试数据文件夹
 345: 
 346: 版本: 2.0
 347: 构建时间: {build_time}
 348:         """.strip()
 349:         
 350:         from datetime import datetime
 351:         build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
 352:         
 353:         info_file = self.dist_dir / "安装说明.txt"
 354:         with open(info_file, 'w', encoding='utf-8') as f:
 355:             f.write(info_content.format(build_time=build_time))
 356:         
 357:         print(f"创建安装说明: {info_file}")
 358: 
 359: 
 360: def main():
 361:     """主函数"""
 362:     parser = argparse.ArgumentParser(description="北斗网格转换工具打包脚本")
 363:     parser.add_argument("--onefile", action="store_true", default=True,
 364:                        help="打包为单个exe文件 (默认)")
 365:     parser.add_argument("--onedir", dest="onefile", action="store_false",
 366:                        help="打包为目录形式")
 367:     parser.add_argument("--console", action="store_true", default=False,
 368:                        help="显示控制台窗口")
 369:     parser.add_argument("--debug", action="store_true", default=False,
 370:                        help="启用调试模式")
 371:     parser.add_argument("--clean-only", action="store_true", default=False,
 372:                        help="仅清理构建文件")
 373:     parser.add_argument("--no-clean", action="store_true", default=False,
 374:                        help="跳过清理步骤")
 375:     
 376:     args = parser.parse_args()
 377:     
 378:     # 创建构建器
 379:     builder = BeidouGridBuilder()
 380:     
 381:     # 仅清理模式
 382:     if args.clean_only:
 383:         builder.clean_build()
 384:         return
 385:     
 386:     print("=" * 60)
 387:     print("北斗网格转换工具 v2.0 - 打包脚本")
 388:     print("=" * 60)
 389:     
 390:     try:
 391:         # 1. 检查依赖
 392:         if not builder.check_dependencies():
 393:             sys.exit(1)
 394: 
 395:         # 2. 构建exe
 396:         success = builder.build_exe(
 397:             onefile=args.onefile,
 398:             console=args.console,
 399:             debug=args.debug
 400:         )
 401:         
 402:         if not success:
 403:             print("构建失败!")
 404:             sys.exit(1)
 405:         
 406:         # 4. 构建后清理
 407:         builder.post_build_cleanup()
 408:         
 409:         # 5. 创建安装说明
 410:         builder.create_installer_info()
 411:         
 412:         print("=" * 60)
 413:         print("构建完成!")
 414:         print(f"输出目录: {builder.dist_dir}")
 415:         
 416:         # 显示输出文件
 417:         if builder.dist_dir.exists():
 418:             print("\n生成的文件:")
 419:             for file_path in builder.dist_dir.iterdir():
 420:                 size = file_path.stat().st_size / (1024 * 1024)  # MB
 421:                 print(f"  {file_path.name} ({size:.1f} MB)")
 422:         
 423:         print("=" * 60)
 424:         
 425:     except KeyboardInterrupt:
 426:         print("\n构建被用户中断")
 427:         sys.exit(1)
 428:     except Exception as e:
 429:         print(f"构建过程中发生错误: {e}")
 430:         import traceback
 431:         traceback.print_exc()
 432:         sys.exit(1)
 433: 
 434: 
 435: if __name__ == "__main__":
 436:     main()
 437: 


================================================================================
文件 4: core\__init__.py
================================================================================

   1: """
   2: 北斗网格转换工具核心模块
   3: """
   4: 
   5: from .grid_calculator import BeidouGridCalculator
   6: from .aggregation import AggregationMethod, DataAggregator
   7: from .coordinate_utils import CoordinateUtils
   8: 
   9: __all__ = [
  10:     'BeidouGridCalculator',
  11:     'AggregationMethod', 
  12:     'DataAggregator',
  13:     'CoordinateUtils'
  14: ]
  15: 


================================================================================
文件 5: core\aggregation.py
================================================================================

   1: """
   2: 数据聚合模块
   3: 提供多种数据聚合方法
   4: """
   5: 
   6: import numpy as np
   7: from enum import Enum
   8: from typing import Union, Callable, Optional
   9: from scipy import stats
  10: 
  11: 
  12: class AggregationMethod(Enum):
  13:     """聚合方法枚举"""
  14:     MAX = "max"
  15:     MIN = "min"
  16:     MEAN = "mean"
  17:     MEDIAN = "median"
  18:     MODE = "mode"
  19:     STD = "std"
  20:     VAR = "var"
  21:     SUM = "sum"
  22:     COUNT = "count"
  23:     PERCENTILE_25 = "p25"
  24:     PERCENTILE_75 = "p75"
  25:     PERCENTILE_90 = "p90"
  26:     PERCENTILE_95 = "p95"
  27:     RANGE = "range"
  28:     IQR = "iqr"  # 四分位距
  29: 
  30: 
  31: class DataAggregator:
  32:     """数据聚合器"""
  33:     
  34:     def __init__(self, method: Union[AggregationMethod, str] = AggregationMethod.MAX,
  35:                  window_size: int = 5, boundary_handling: str = "edge"):
  36:         """
  37:         初始化聚合器
  38:         
  39:         Args:
  40:             method: 聚合方法
  41:             window_size: 窗口大小
  42:             boundary_handling: 边界处理方式 ("edge", "constant", "reflect", "wrap")
  43:         """
  44:         if isinstance(method, str):
  45:             method = AggregationMethod(method)
  46:         
  47:         self.method = method
  48:         self.window_size = window_size
  49:         self.boundary_handling = boundary_handling
  50:         
  51:         # 聚合函数映射
  52:         self._aggregation_functions = {
  53:             AggregationMethod.MAX: self._max_aggregation,
  54:             AggregationMethod.MIN: self._min_aggregation,
  55:             AggregationMethod.MEAN: self._mean_aggregation,
  56:             AggregationMethod.MEDIAN: self._median_aggregation,
  57:             AggregationMethod.MODE: self._mode_aggregation,
  58:             AggregationMethod.STD: self._std_aggregation,
  59:             AggregationMethod.VAR: self._var_aggregation,
  60:             AggregationMethod.SUM: self._sum_aggregation,
  61:             AggregationMethod.COUNT: self._count_aggregation,
  62:             AggregationMethod.PERCENTILE_25: lambda x: self._percentile_aggregation(x, 25),
  63:             AggregationMethod.PERCENTILE_75: lambda x: self._percentile_aggregation(x, 75),
  64:             AggregationMethod.PERCENTILE_90: lambda x: self._percentile_aggregation(x, 90),
  65:             AggregationMethod.PERCENTILE_95: lambda x: self._percentile_aggregation(x, 95),
  66:             AggregationMethod.RANGE: self._range_aggregation,
  67:             AggregationMethod.IQR: self._iqr_aggregation,
  68:         }
  69:     
  70:     def aggregate(self, array: np.ndarray, nodata_value: Optional[float] = None, progress_callback=None, cancel_check=None) -> np.ndarray:
  71:         """
  72:         对数组进行聚合处理
  73:         
  74:         Args:
  75:             array: 输入数组
  76:             nodata_value: 无效值
  77:             
  78:         Returns:
  79:             聚合后的数组
  80:         """
  81:         if array.ndim != 2:
  82:             raise ValueError("输入数组必须是二维数组")
  83:         
  84:         rows, cols = array.shape
  85:         
  86:         # 处理边界
  87:         pad_width = self.window_size // 2
  88:         if self.boundary_handling == "constant":
  89:             padded_array = np.pad(array, pad_width=pad_width, 
  90:                                 mode='constant', constant_values=nodata_value or 0)
  91:         else:
  92:             padded_array = np.pad(array, pad_width=pad_width, mode=self.boundary_handling)
  93:         
  94:         result = np.zeros_like(array, dtype=float)
  95:         aggregation_func = self._aggregation_functions[self.method]
  96:         
  97:         # 滑动窗口处理
  98:         total_pixels = rows * cols
  99:         processed_pixels = 0
 100: 
 101:         for i in range(rows):
 102:             for j in range(cols):
 103:                 # 提取窗口
 104:                 window = padded_array[i:i + self.window_size, j:j + self.window_size]
 105: 
 106:                 # 处理无效值
 107:                 if nodata_value is not None:
 108:                     window = window[window != nodata_value]
 109:                     if window.size == 0:
 110:                         result[i, j] = nodata_value
 111:                         processed_pixels += 1
 112:                         continue
 113: 
 114:                 # 应用聚合函数
 115:                 result[i, j] = aggregation_func(window)
 116:                 processed_pixels += 1
 117: 
 118:                 # 检查是否取消（每处理1000个像素检查一次）
 119:                 if processed_pixels % 1000 == 0:
 120:                     if cancel_check:
 121:                         cancel_check()
 122: 
 123:                     # 更新进度
 124:                     if progress_callback:
 125:                         progress_percent = int((processed_pixels / total_pixels) * 100)
 126:                         detail = f"已处理 {processed_pixels:,} / {total_pixels:,} 像素 ({progress_percent}%)"
 127:                         # 传递像素处理的详细信息用于时间估算
 128:                         stage_progress = 40 + int(progress_percent * 0.2)
 129:                         progress_callback(stage_progress, "执行数据聚合", detail, {
 130:                             'pixels_processed': processed_pixels,
 131:                             'total_pixels': total_pixels,
 132:                             'stage_percent': progress_percent
 133:                         })
 134:         
 135:         return result
 136:     
 137:     def aggregate_blocks(self, array: np.ndarray, block_shape: tuple,
 138:                         nodata_value: Optional[float] = None) -> np.ndarray:
 139:         """
 140:         按块进行聚合
 141:         
 142:         Args:
 143:             array: 输入数组
 144:             block_shape: 块的形状 (block_rows, block_cols)
 145:             nodata_value: 无效值
 146:             
 147:         Returns:
 148:             聚合后的数组
 149:         """
 150:         rows, cols = array.shape
 151:         block_rows, block_cols = block_shape
 152:         
 153:         # 计算输出数组大小
 154:         out_rows = int(np.ceil(rows / block_rows))
 155:         out_cols = int(np.ceil(cols / block_cols))
 156:         
 157:         result = np.zeros((out_rows, out_cols), dtype=float)
 158:         aggregation_func = self._aggregation_functions[self.method]
 159:         
 160:         for i in range(out_rows):
 161:             for j in range(out_cols):
 162:                 # 计算块的边界
 163:                 start_row = i * block_rows
 164:                 end_row = min((i + 1) * block_rows, rows)
 165:                 start_col = j * block_cols
 166:                 end_col = min((j + 1) * block_cols, cols)
 167:                 
 168:                 # 提取块
 169:                 block = array[start_row:end_row, start_col:end_col]
 170:                 
 171:                 # 处理无效值
 172:                 if nodata_value is not None:
 173:                     valid_data = block[block != nodata_value]
 174:                     if valid_data.size == 0:
 175:                         result[i, j] = nodata_value
 176:                         continue
 177:                     block = valid_data
 178:                 
 179:                 # 应用聚合函数
 180:                 result[i, j] = aggregation_func(block)
 181:         
 182:         return result
 183:     
 184:     # 聚合函数实现
 185:     def _max_aggregation(self, data: np.ndarray) -> float:
 186:         """最大值聚合"""
 187:         return np.max(data) if data.size > 0 else 0.0
 188:     
 189:     def _min_aggregation(self, data: np.ndarray) -> float:
 190:         """最小值聚合"""
 191:         return np.min(data) if data.size > 0 else 0.0
 192:     
 193:     def _mean_aggregation(self, data: np.ndarray) -> float:
 194:         """平均值聚合"""
 195:         return np.mean(data) if data.size > 0 else 0.0
 196:     
 197:     def _median_aggregation(self, data: np.ndarray) -> float:
 198:         """中位数聚合"""
 199:         return np.median(data) if data.size > 0 else 0.0
 200:     
 201:     def _mode_aggregation(self, data: np.ndarray) -> float:
 202:         """众数聚合"""
 203:         if data.size == 0:
 204:             return 0.0
 205:         try:
 206:             mode_result = stats.mode(data, keepdims=True)
 207:             return float(mode_result.mode[0])
 208:         except:
 209:             return float(data[0])  # 如果无法计算众数，返回第一个值
 210:     
 211:     def _std_aggregation(self, data: np.ndarray) -> float:
 212:         """标准差聚合"""
 213:         return np.std(data) if data.size > 0 else 0.0
 214:     
 215:     def _var_aggregation(self, data: np.ndarray) -> float:
 216:         """方差聚合"""
 217:         return np.var(data) if data.size > 0 else 0.0
 218:     
 219:     def _sum_aggregation(self, data: np.ndarray) -> float:
 220:         """求和聚合"""
 221:         return np.sum(data) if data.size > 0 else 0.0
 222:     
 223:     def _count_aggregation(self, data: np.ndarray) -> float:
 224:         """计数聚合"""
 225:         return float(data.size)
 226:     
 227:     def _percentile_aggregation(self, data: np.ndarray, percentile: float) -> float:
 228:         """百分位数聚合"""
 229:         return np.percentile(data, percentile) if data.size > 0 else 0.0
 230:     
 231:     def _range_aggregation(self, data: np.ndarray) -> float:
 232:         """极差聚合"""
 233:         return (np.max(data) - np.min(data)) if data.size > 0 else 0.0
 234:     
 235:     def _iqr_aggregation(self, data: np.ndarray) -> float:
 236:         """四分位距聚合"""
 237:         if data.size == 0:
 238:             return 0.0
 239:         q75 = np.percentile(data, 75)
 240:         q25 = np.percentile(data, 25)
 241:         return q75 - q25
 242:     
 243:     @staticmethod
 244:     def get_available_methods() -> list:
 245:         """获取所有可用的聚合方法"""
 246:         return [method.value for method in AggregationMethod]
 247:     
 248:     @staticmethod
 249:     def get_method_description(method: Union[AggregationMethod, str]) -> str:
 250:         """获取聚合方法的描述"""
 251:         if isinstance(method, str):
 252:             method = AggregationMethod(method)
 253:         
 254:         descriptions = {
 255:             AggregationMethod.MAX: "最大值",
 256:             AggregationMethod.MIN: "最小值",
 257:             AggregationMethod.MEAN: "平均值",
 258:             AggregationMethod.MEDIAN: "中位数",
 259:             AggregationMethod.MODE: "众数",
 260:             AggregationMethod.STD: "标准差",
 261:             AggregationMethod.VAR: "方差",
 262:             AggregationMethod.SUM: "求和",
 263:             AggregationMethod.COUNT: "计数",
 264:             AggregationMethod.PERCENTILE_25: "25%分位数",
 265:             AggregationMethod.PERCENTILE_75: "75%分位数",
 266:             AggregationMethod.PERCENTILE_90: "90%分位数",
 267:             AggregationMethod.PERCENTILE_95: "95%分位数",
 268:             AggregationMethod.RANGE: "极差",
 269:             AggregationMethod.IQR: "四分位距",
 270:         }
 271:         
 272:         return descriptions.get(method, "未知方法")
 273: 


================================================================================
文件 6: core\coordinate_utils.py
================================================================================

   1: """
   2: 坐标转换工具模块
   3: """
   4: 
   5: import math
   6: import numpy as np
   7: from typing import Tuple, List, Optional
   8: 
   9: 
  10: class CoordinateUtils:
  11:     """坐标转换工具类"""
  12:     
  13:     @staticmethod
  14:     def pixel_to_geo(pixel_x: int, pixel_y: int, transform: tuple) -> Tuple[float, float]:
  15:         """
  16:         像素坐标转地理坐标
  17:         
  18:         Args:
  19:             pixel_x, pixel_y: 像素坐标
  20:             transform: 仿射变换参数 (x_origin, pixel_width, 0, y_origin, 0, -pixel_height)
  21:             
  22:         Returns:
  23:             (lon, lat): 地理坐标
  24:         """
  25:         x_origin, pixel_width, _, y_origin, _, pixel_height = transform
  26:         
  27:         lon = x_origin + pixel_x * pixel_width
  28:         lat = y_origin + pixel_y * pixel_height
  29:         
  30:         return lon, lat
  31:     
  32:     @staticmethod
  33:     def geo_to_pixel(lon: float, lat: float, transform: tuple) -> Tuple[int, int]:
  34:         """
  35:         地理坐标转像素坐标
  36:         
  37:         Args:
  38:             lon, lat: 地理坐标
  39:             transform: 仿射变换参数
  40:             
  41:         Returns:
  42:             (pixel_x, pixel_y): 像素坐标
  43:         """
  44:         x_origin, pixel_width, _, y_origin, _, pixel_height = transform
  45:         
  46:         pixel_x = int((lon - x_origin) / pixel_width)
  47:         pixel_y = int((lat - y_origin) / pixel_height)
  48:         
  49:         return pixel_x, pixel_y
  50:     
  51:     @staticmethod
  52:     def calculate_bounds(center_lat: float, center_lon: float, 
  53:                         grid_level: int) -> Tuple[float, float, float, float]:
  54:         """
  55:         根据中心点和网格级别计算边界
  56:         
  57:         Args:
  58:             center_lat, center_lon: 中心点坐标
  59:             grid_level: 网格级别
  60:             
  61:         Returns:
  62:             (min_lon, min_lat, max_lon, max_lat): 边界坐标
  63:         """
  64:         from core.grid_calculator import BeidouGridCalculator
  65:         
  66:         calculator = BeidouGridCalculator()
  67:         lon_size, lat_size = calculator.get_grid_size(grid_level)
  68:         
  69:         min_lon = center_lon - lon_size / 2
  70:         max_lon = center_lon + lon_size / 2
  71:         min_lat = center_lat - lat_size / 2
  72:         max_lat = center_lat + lat_size / 2
  73:         
  74:         return min_lon, min_lat, max_lon, max_lat
  75:     
  76:     @staticmethod
  77:     def validate_coordinates(lat: float, lon: float) -> bool:
  78:         """
  79:         验证坐标是否有效
  80:         
  81:         Args:
  82:             lat: 纬度
  83:             lon: 经度
  84:             
  85:         Returns:
  86:             是否有效
  87:         """
  88:         return -90 <= lat <= 90 and -180 <= lon <= 180
  89:     
  90:     @staticmethod
  91:     def normalize_longitude(lon: float) -> float:
  92:         """
  93:         标准化经度到[-180, 180]范围
  94:         
  95:         Args:
  96:             lon: 经度
  97:             
  98:         Returns:
  99:             标准化后的经度
 100:         """
 101:         while lon > 180:
 102:             lon -= 360
 103:         while lon < -180:
 104:             lon += 360
 105:         return lon
 106:     
 107:     @staticmethod
 108:     def calculate_distance(lat1: float, lon1: float, 
 109:                           lat2: float, lon2: float) -> float:
 110:         """
 111:         计算两点间的大圆距离（米）
 112:         
 113:         Args:
 114:             lat1, lon1: 第一个点的坐标
 115:             lat2, lon2: 第二个点的坐标
 116:             
 117:         Returns:
 118:             距离（米）
 119:         """
 120:         # 地球半径（米）
 121:         R = 6371000
 122:         
 123:         # 转换为弧度
 124:         lat1_rad = math.radians(lat1)
 125:         lon1_rad = math.radians(lon1)
 126:         lat2_rad = math.radians(lat2)
 127:         lon2_rad = math.radians(lon2)
 128:         
 129:         # Haversine公式
 130:         dlat = lat2_rad - lat1_rad
 131:         dlon = lon2_rad - lon1_rad
 132:         
 133:         a = (math.sin(dlat/2)**2 + 
 134:              math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
 135:         c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
 136:         
 137:         return R * c
 138:     
 139:     @staticmethod
 140:     def calculate_grid_area(lat: float, grid_level: int) -> float:
 141:         """
 142:         计算指定纬度和网格级别的网格面积（平方米）
 143:         
 144:         Args:
 145:             lat: 纬度
 146:             grid_level: 网格级别
 147:             
 148:         Returns:
 149:             面积（平方米）
 150:         """
 151:         from core.grid_calculator import BeidouGridCalculator
 152:         
 153:         calculator = BeidouGridCalculator()
 154:         lon_size, lat_size = calculator.get_grid_size(grid_level)
 155:         
 156:         # 地球半径（米）
 157:         R = 6371000
 158:         
 159:         # 转换为弧度
 160:         lat_rad = math.radians(lat)
 161:         lon_size_rad = math.radians(lon_size)
 162:         lat_size_rad = math.radians(lat_size)
 163:         
 164:         # 计算面积
 165:         area = R**2 * lon_size_rad * lat_size_rad * math.cos(lat_rad)
 166:         
 167:         return abs(area)
 168:     
 169:     @staticmethod
 170:     def create_grid_polygon(center_lat: float, center_lon: float, 
 171:                            grid_level: int) -> List[Tuple[float, float]]:
 172:         """
 173:         创建网格多边形的顶点坐标
 174:         
 175:         Args:
 176:             center_lat, center_lon: 中心点坐标
 177:             grid_level: 网格级别
 178:             
 179:         Returns:
 180:             多边形顶点坐标列表
 181:         """
 182:         min_lon, min_lat, max_lon, max_lat = CoordinateUtils.calculate_bounds(
 183:             center_lat, center_lon, grid_level)
 184:         
 185:         # 返回矩形的四个顶点（逆时针）
 186:         return [
 187:             (min_lon, min_lat),  # 左下
 188:             (max_lon, min_lat),  # 右下
 189:             (max_lon, max_lat),  # 右上
 190:             (min_lon, max_lat),  # 左上
 191:             (min_lon, min_lat)   # 闭合
 192:         ]
 193:     
 194:     @staticmethod
 195:     def degrees_to_dms(degrees: float) -> Tuple[int, int, float]:
 196:         """
 197:         度转度分秒
 198:         
 199:         Args:
 200:             degrees: 度数
 201:             
 202:         Returns:
 203:             (度, 分, 秒)
 204:         """
 205:         abs_degrees = abs(degrees)
 206:         d = int(abs_degrees)
 207:         m = int((abs_degrees - d) * 60)
 208:         s = ((abs_degrees - d) * 60 - m) * 60
 209:         
 210:         if degrees < 0:
 211:             d = -d
 212:         
 213:         return d, m, s
 214:     
 215:     @staticmethod
 216:     def dms_to_degrees(degrees: int, minutes: int, seconds: float) -> float:
 217:         """
 218:         度分秒转度
 219:         
 220:         Args:
 221:             degrees: 度
 222:             minutes: 分
 223:             seconds: 秒
 224:             
 225:         Returns:
 226:             度数
 227:         """
 228:         result = abs(degrees) + minutes/60 + seconds/3600
 229:         return result if degrees >= 0 else -result
 230:     
 231:     @staticmethod
 232:     def format_coordinate(lat: float, lon: float, format_type: str = "decimal") -> str:
 233:         """
 234:         格式化坐标显示
 235:         
 236:         Args:
 237:             lat: 纬度
 238:             lon: 经度
 239:             format_type: 格式类型 ("decimal", "dms", "dm")
 240:             
 241:         Returns:
 242:             格式化后的坐标字符串
 243:         """
 244:         if format_type == "decimal":
 245:             return f"{lat:.6f}, {lon:.6f}"
 246:         
 247:         elif format_type == "dms":
 248:             lat_d, lat_m, lat_s = CoordinateUtils.degrees_to_dms(lat)
 249:             lon_d, lon_m, lon_s = CoordinateUtils.degrees_to_dms(lon)
 250:             
 251:             lat_dir = "N" if lat >= 0 else "S"
 252:             lon_dir = "E" if lon >= 0 else "W"
 253:             
 254:             return (f"{abs(lat_d)}°{lat_m}'{lat_s:.2f}\"{lat_dir}, "
 255:                    f"{abs(lon_d)}°{lon_m}'{lon_s:.2f}\"{lon_dir}")
 256:         
 257:         elif format_type == "dm":
 258:             lat_d = int(lat)
 259:             lat_m = (lat - lat_d) * 60
 260:             lon_d = int(lon)
 261:             lon_m = (lon - lon_d) * 60
 262:             
 263:             lat_dir = "N" if lat >= 0 else "S"
 264:             lon_dir = "E" if lon >= 0 else "W"
 265:             
 266:             return (f"{abs(lat_d)}°{abs(lat_m):.4f}'{lat_dir}, "
 267:                    f"{abs(lon_d)}°{abs(lon_m):.4f}'{lon_dir}")
 268:         
 269:         else:
 270:             raise ValueError(f"不支持的格式类型: {format_type}")
 271:     
 272:     @staticmethod
 273:     def create_transform_from_bounds(min_lon: float, min_lat: float,
 274:                                    max_lon: float, max_lat: float,
 275:                                    width: int, height: int) -> tuple:
 276:         """
 277:         根据边界和尺寸创建仿射变换参数
 278:         
 279:         Args:
 280:             min_lon, min_lat, max_lon, max_lat: 边界坐标
 281:             width, height: 图像宽高
 282:             
 283:         Returns:
 284:             仿射变换参数
 285:         """
 286:         pixel_width = (max_lon - min_lon) / width
 287:         pixel_height = (max_lat - min_lat) / height
 288:         
 289:         return (min_lon, pixel_width, 0, max_lat, 0, -pixel_height)
 290: 


================================================================================
文件 7: core\grid_calculator.py
================================================================================

   1: """
   2: 北斗网格计算模块
   3: 重构并优化的北斗网格编码算法
   4: """
   5: 
   6: import math
   7: import numpy as np
   8: from typing import Tuple, Dict, List, Optional
   9: from dataclasses import dataclass
  10: 
  11: 
  12: @dataclass
  13: class GridLevel:
  14:     """网格级别配置"""
  15:     level: int
  16:     lon_size: float  # 经度尺寸（度）
  17:     lat_size: float  # 纬度尺寸（度）
  18:     description: str
  19: 
  20: 
  21: class BeidouGridCalculator:
  22:     """北斗网格计算器"""
  23:     
  24:     # 网格级别配置表
  25:     GRID_LEVELS = {
  26:         1: GridLevel(1, 6.0, 4.0, "一级网格：6°×4°"),
  27:         2: GridLevel(2, 30.0/60, 30.0/60, "二级网格：30'×30'"),
  28:         3: GridLevel(3, 15.0/60, 10.0/60, "三级网格：15'×10'"),
  29:         4: GridLevel(4, 1.0/60, 1.0/60, "四级网格：1'×1'"),
  30:         5: GridLevel(5, 4.0/3600, 4.0/3600, "五级网格：4\"×4\""),
  31:         6: GridLevel(6, 2.0/3600, 2.0/3600, "六级网格：2\"×2\""),
  32:         7: GridLevel(7, 1.0/14400, 1.0/14400, "七级网格：1/4\"×1/4\""),
  33:         8: GridLevel(8, 1.0/115200, 1.0/115200, "八级网格：1/32\"×1/32\""),
  34:         9: GridLevel(9, 1.0/921600, 1.0/921600, "九级网格：1/256\"×1/256\""),
  35:         10: GridLevel(10, 1.0/7372800, 1.0/7372800, "十级网格：1/2048\"×1/2048\"")
  36:     }
  37:     
  38:     def __init__(self):
  39:         """初始化网格计算器"""
  40:         self._cache = {}  # 编码缓存
  41:     
  42:     def get_grid_size(self, level: int) -> Tuple[float, float]:
  43:         """
  44:         获取指定级别的网格尺寸
  45:         
  46:         Args:
  47:             level: 网格级别 (1-10)
  48:             
  49:         Returns:
  50:             (lon_size, lat_size): 经纬度尺寸
  51:         """
  52:         if level not in self.GRID_LEVELS:
  53:             raise ValueError(f"不支持的网格级别: {level}，支持范围: 1-10")
  54:         
  55:         grid_level = self.GRID_LEVELS[level]
  56:         return grid_level.lon_size, grid_level.lat_size
  57:     
  58:     def calculate_grid_dimensions(self, min_lon: float, min_lat: float, 
  59:                                 max_lon: float, max_lat: float, 
  60:                                 level: int) -> Tuple[int, int]:
  61:         """
  62:         计算网格维度
  63:         
  64:         Args:
  65:             min_lon, min_lat: 最小经纬度
  66:             max_lon, max_lat: 最大经纬度
  67:             level: 网格级别
  68:             
  69:         Returns:
  70:             (rows, cols): 网格行列数
  71:         """
  72:         lon_size, lat_size = self.get_grid_size(level)
  73:         
  74:         cols = int(math.ceil((max_lon - min_lon) / lon_size))
  75:         rows = int(math.ceil((max_lat - min_lat) / lat_size))
  76:         
  77:         return rows, cols
  78:     
  79:     def calculate_grid_code(self, lat: float, lon: float, level: int) -> str:
  80:         """
  81:         计算经纬度对应的北斗网格编码
  82:         
  83:         Args:
  84:             lat: 纬度
  85:             lon: 经度
  86:             level: 网格级别 (1-10)
  87:             
  88:         Returns:
  89:             网格编码字符串
  90:         """
  91:         if level < 1 or level > 10:
  92:             raise ValueError(f"网格级别必须在1-10之间，当前值: {level}")
  93:         
  94:         # 检查缓存
  95:         cache_key = (lat, lon, level)
  96:         if cache_key in self._cache:
  97:             return self._cache[cache_key]
  98:         
  99:         # 逐级计算编码
 100:         codes = {}
 101:         
 102:         # 一级网格
 103:         codes[1] = self._calculate_first_level(lat, lon)
 104:         
 105:         if level >= 2:
 106:             codes[2] = self._calculate_second_level(lat, lon, codes[1])
 107:         
 108:         if level >= 3:
 109:             codes[3] = self._calculate_third_level(lat, lon, codes[2])
 110:         
 111:         if level >= 4:
 112:             codes[4] = self._calculate_fourth_level(lat, lon, codes[3])
 113:         
 114:         if level >= 5:
 115:             codes[5] = self._calculate_fifth_level(lat, lon, codes[4])
 116:         
 117:         if level >= 6:
 118:             codes[6] = self._calculate_sixth_level(lat, lon, codes[5])
 119:         
 120:         if level >= 7:
 121:             codes[7] = self._calculate_seventh_level(lat, lon, codes[6])
 122:         
 123:         if level >= 8:
 124:             codes[8] = self._calculate_eighth_level(lat, lon, codes[7])
 125:         
 126:         if level >= 9:
 127:             codes[9] = self._calculate_ninth_level(lat, lon, codes[8])
 128:         
 129:         if level >= 10:
 130:             codes[10] = self._calculate_tenth_level(lat, lon, codes[9])
 131:         
 132:         result = codes[level]
 133:         self._cache[cache_key] = result
 134:         return result
 135:     
 136:     def _calculate_first_level(self, lat: float, lon: float) -> str:
 137:         """计算一级网格编码：6°×4°"""
 138:         code1 = 'N' if lat >= 0 else 'S'
 139:         code23 = str(int((lon + 180) / 6) + 1).zfill(2)
 140:         code4 = chr(ord('A') + int(abs(lat) / 4))
 141:         return code1 + code23 + code4
 142:     
 143:     def _calculate_second_level(self, lat: float, lon: float, first_code: str) -> str:
 144:         """计算二级网格编码：30'×30'"""
 145:         lon_base = int(lon / 6) * 6
 146:         lat_base = int(abs(lat) / 4) * 4
 147:         
 148:         a2 = int((lon - lon_base) * 2) + 1
 149:         b2 = int((abs(lat) - lat_base) * 2) + 1
 150:         
 151:         code5 = a2 - 1
 152:         code6 = b2 - 1
 153:         
 154:         # 处理特殊情况
 155:         if code6 >= 10:
 156:             code6_dict = {10: 'A', 11: 'B'}
 157:             code6 = code6_dict.get(code6, str(code6))
 158:         
 159:         return first_code + str(code5) + str(code6)
 160:     
 161:     def _calculate_third_level(self, lat: float, lon: float, second_code: str) -> str:
 162:         """计算三级网格编码：15'×10'"""
 163:         # 获取二级网格基准点
 164:         lon_base, lat_base = self._get_second_level_base(lat, lon)
 165:         
 166:         # Z字形编码数组
 167:         array3 = [[0, 2, 4], [1, 3, 5]]
 168:         
 169:         lon_index = 1 if (lon - lon_base) * 4 >= 1 else 0
 170:         lat_diff = (abs(lat) - lat_base) * 6
 171:         
 172:         if 0 < lat_diff <= 1:
 173:             lat_index = 0
 174:         elif 1 < lat_diff <= 2:
 175:             lat_index = 1
 176:         else:  # 2 < lat_diff <= 3
 177:             lat_index = 2
 178:         
 179:         code7 = array3[lon_index][lat_index]
 180:         return second_code + str(code7)
 181:     
 182:     def _get_second_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 183:         """获取二级网格基准点"""
 184:         lon_base1 = int(lon / 6) * 6
 185:         lat_base1 = int(abs(lat) / 4) * 4
 186:         
 187:         a2 = int((lon - lon_base1) * 2) + 1
 188:         b2 = int((abs(lat) - lat_base1) * 2) + 1
 189:         
 190:         lon_base2 = lon_base1 + (a2 - 1) * 0.5
 191:         lat_base2 = lat_base1 + (b2 - 1) * 0.5
 192:         
 193:         return lon_base2, lat_base2
 194: 
 195:     def _calculate_fourth_level(self, lat: float, lon: float, third_code: str) -> str:
 196:         """计算四级网格编码：1'×1'"""
 197:         lon_base, lat_base = self._get_third_level_base(lat, lon)
 198: 
 199:         a4 = int((lon - lon_base) * 60) + 1
 200:         b4 = int((abs(lat) - lat_base) * 60) + 1
 201: 
 202:         code8 = a4 - 1
 203:         code9 = b4 - 1
 204: 
 205:         # 处理大于9的情况
 206:         code8_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
 207:         if code8 >= 10:
 208:             code8 = code8_dict.get(code8, str(code8))
 209: 
 210:         return third_code + str(code8) + str(code9)
 211: 
 212:     def _calculate_fifth_level(self, lat: float, lon: float, fourth_code: str) -> str:
 213:         """计算五级网格编码：4"×4" """
 214:         lon_base, lat_base = self._get_fourth_level_base(lat, lon)
 215: 
 216:         a5 = int((lon - lon_base) * 900) + 1
 217:         b5 = int((abs(lat) - lat_base) * 900) + 1
 218: 
 219:         code10 = a5 - 1
 220:         code11 = b5 - 1
 221: 
 222:         # 处理大于9的情况
 223:         code_dict = {10: 'A', 11: 'B', 12: 'C', 13: 'D', 14: 'E'}
 224:         if code10 >= 10:
 225:             code10 = code_dict.get(code10, str(code10))
 226:         if code11 >= 10:
 227:             code11 = code_dict.get(code11, str(code11))
 228: 
 229:         return fourth_code + str(code10) + str(code11)
 230: 
 231:     def _calculate_sixth_level(self, lat: float, lon: float, fifth_code: str) -> str:
 232:         """计算六级网格编码：2"×2" """
 233:         lon_base, lat_base = self._get_fifth_level_base(lat, lon)
 234: 
 235:         # Z字形编码数组
 236:         array6 = [[0, 2], [1, 3]]
 237: 
 238:         lon_index = 1 if (lon - lon_base) * 1800 >= 1 else 0
 239:         lat_index = 1 if (abs(lat) - lat_base) * 1800 >= 1 else 0
 240: 
 241:         code12 = array6[lon_index][lat_index]
 242:         return fifth_code + str(code12)
 243: 
 244:     def _calculate_seventh_level(self, lat: float, lon: float, sixth_code: str) -> str:
 245:         """计算七级网格编码：1/4"×1/4" """
 246:         lon_base, lat_base = self._get_sixth_level_base(lat, lon)
 247: 
 248:         a7 = int((lon - lon_base) * 14400) + 1
 249:         b7 = int((abs(lat) - lat_base) * 14400) + 1
 250: 
 251:         code13 = a7 - 1
 252:         code14 = b7 - 1
 253: 
 254:         return sixth_code + str(code13) + str(code14)
 255: 
 256:     def _calculate_eighth_level(self, lat: float, lon: float, seventh_code: str) -> str:
 257:         """计算八级网格编码：1/32"×1/32" """
 258:         lon_base, lat_base = self._get_seventh_level_base(lat, lon)
 259: 
 260:         a8 = int((lon - lon_base) * 115200) + 1
 261:         b8 = int((abs(lat) - lat_base) * 115200) + 1
 262: 
 263:         code15 = a8 - 1
 264:         code16 = b8 - 1
 265: 
 266:         return seventh_code + str(code15) + str(code16)
 267: 
 268:     def _calculate_ninth_level(self, lat: float, lon: float, eighth_code: str) -> str:
 269:         """计算九级网格编码：1/256"×1/256" """
 270:         lon_base, lat_base = self._get_eighth_level_base(lat, lon)
 271: 
 272:         a9 = int((lon - lon_base) * 921600) + 1
 273:         b9 = int((abs(lat) - lat_base) * 921600) + 1
 274: 
 275:         code17 = a9 - 1
 276:         code18 = b9 - 1
 277: 
 278:         return eighth_code + str(code17) + str(code18)
 279: 
 280:     def _calculate_tenth_level(self, lat: float, lon: float, ninth_code: str) -> str:
 281:         """计算十级网格编码：1/2048"×1/2048" """
 282:         lon_base, lat_base = self._get_ninth_level_base(lat, lon)
 283: 
 284:         a10 = int((lon - lon_base) * 7372800) + 1
 285:         b10 = int((abs(lat) - lat_base) * 7372800) + 1
 286: 
 287:         code19 = a10 - 1
 288:         code20 = b10 - 1
 289: 
 290:         return ninth_code + str(code19) + str(code20)
 291: 
 292:     def _get_third_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 293:         """获取三级网格基准点"""
 294:         lon_base2, lat_base2 = self._get_second_level_base(lat, lon)
 295: 
 296:         a3 = int((lon - lon_base2) * 4) + 1
 297:         b3 = int((abs(lat) - lat_base2) * 6) + 1
 298: 
 299:         lon_base3 = lon_base2 + (a3 - 1) * 0.25
 300:         lat_base3 = lat_base2 + (b3 - 1) * (1.0 / 6)
 301: 
 302:         return lon_base3, lat_base3
 303: 
 304:     def _get_fourth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 305:         """获取四级网格基准点"""
 306:         lon_base3, lat_base3 = self._get_third_level_base(lat, lon)
 307: 
 308:         a4 = int((lon - lon_base3) * 60) + 1
 309:         b4 = int((abs(lat) - lat_base3) * 60) + 1
 310: 
 311:         lon_base4 = lon_base3 + (a4 - 1) * (1.0 / 60)
 312:         lat_base4 = lat_base3 + (b4 - 1) * (1.0 / 60)
 313: 
 314:         return lon_base4, lat_base4
 315: 
 316:     def _get_fifth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 317:         """获取五级网格基准点"""
 318:         lon_base4, lat_base4 = self._get_fourth_level_base(lat, lon)
 319: 
 320:         a5 = int((lon - lon_base4) * 900) + 1
 321:         b5 = int((abs(lat) - lat_base4) * 900) + 1
 322: 
 323:         lon_base5 = lon_base4 + (a5 - 1) * (1.0 / 900)
 324:         lat_base5 = lat_base4 + (b5 - 1) * (1.0 / 900)
 325: 
 326:         return lon_base5, lat_base5
 327: 
 328:     def _get_sixth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 329:         """获取六级网格基准点"""
 330:         lon_base5, lat_base5 = self._get_fifth_level_base(lat, lon)
 331: 
 332:         a6 = int((lon - lon_base5) * 1800) + 1
 333:         b6 = int((abs(lat) - lat_base5) * 1800) + 1
 334: 
 335:         lon_base6 = lon_base5 + (a6 - 1) * (1.0 / 1800)
 336:         lat_base6 = lat_base5 + (b6 - 1) * (1.0 / 1800)
 337: 
 338:         return lon_base6, lat_base6
 339: 
 340:     def _get_seventh_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 341:         """获取七级网格基准点"""
 342:         lon_base6, lat_base6 = self._get_sixth_level_base(lat, lon)
 343: 
 344:         a7 = int((lon - lon_base6) * 14400) + 1
 345:         b7 = int((abs(lat) - lat_base6) * 14400) + 1
 346: 
 347:         lon_base7 = lon_base6 + (a7 - 1) * (1.0 / 14400)
 348:         lat_base7 = lat_base6 + (b7 - 1) * (1.0 / 14400)
 349: 
 350:         return lon_base7, lat_base7
 351: 
 352:     def _get_eighth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 353:         """获取八级网格基准点"""
 354:         lon_base7, lat_base7 = self._get_seventh_level_base(lat, lon)
 355: 
 356:         a8 = int((lon - lon_base7) * 115200) + 1
 357:         b8 = int((abs(lat) - lat_base7) * 115200) + 1
 358: 
 359:         lon_base8 = lon_base7 + (a8 - 1) * (1.0 / 115200)
 360:         lat_base8 = lat_base7 + (b8 - 1) * (1.0 / 115200)
 361: 
 362:         return lon_base8, lat_base8
 363: 
 364:     def _get_ninth_level_base(self, lat: float, lon: float) -> Tuple[float, float]:
 365:         """获取九级网格基准点"""
 366:         lon_base8, lat_base8 = self._get_eighth_level_base(lat, lon)
 367: 
 368:         a9 = int((lon - lon_base8) * 921600) + 1
 369:         b9 = int((abs(lat) - lat_base8) * 921600) + 1
 370: 
 371:         lon_base9 = lon_base8 + (a9 - 1) * (1.0 / 921600)
 372:         lat_base9 = lat_base8 + (b9 - 1) * (1.0 / 921600)
 373: 
 374:         return lon_base9, lat_base9
 375: 
 376:     def clear_cache(self):
 377:         """清空编码缓存"""
 378:         self._cache.clear()
 379: 
 380:     def get_cache_size(self) -> int:
 381:         """获取缓存大小"""
 382:         return len(self._cache)
 383: 
 384:     def batch_calculate_codes(self, coordinates: List[Tuple[float, float]],
 385:                             level: int) -> List[str]:
 386:         """
 387:         批量计算网格编码
 388: 
 389:         Args:
 390:             coordinates: 坐标列表 [(lat, lon), ...]
 391:             level: 网格级别
 392: 
 393:         Returns:
 394:             编码列表
 395:         """
 396:         return [self.calculate_grid_code(lat, lon, level)
 397:                 for lat, lon in coordinates]
 398: 


================================================================================
文件 8: data\__init__.py
================================================================================

   1: """
   2: 数据处理模块
   3: """
   4: 
   5: # 延迟导入以避免依赖问题
   6: def get_raster_io():
   7:     from .raster_io import RasterReader, RasterWriter
   8:     return RasterReader, RasterWriter
   9: 
  10: def get_vector_io():
  11:     from .vector_io import VectorWriter
  12:     return VectorWriter
  13: 
  14: def get_data_validator():
  15:     from .data_validator import DataValidator
  16:     return DataValidator
  17: 
  18: __all__ = [
  19:     'get_raster_io',
  20:     'get_vector_io',
  21:     'get_data_validator'
  22: ]
  23: 


================================================================================
文件 9: data\data_validator.py
================================================================================

   1: """
   2: 数据验证模块
   3: """
   4: 
   5: import os
   6: import numpy as np
   7: from typing import Tuple, Optional, List, Dict, Any
   8: import logging
   9: 
  10: 
  11: class DataValidator:
  12:     """数据验证器"""
  13:     
  14:     @staticmethod
  15:     def validate_file_path(file_path: str, check_exists: bool = True) -> bool:
  16:         """
  17:         验证文件路径
  18:         
  19:         Args:
  20:             file_path: 文件路径
  21:             check_exists: 是否检查文件存在
  22:             
  23:         Returns:
  24:             是否有效
  25:         """
  26:         if not file_path or not isinstance(file_path, str):
  27:             logging.error("文件路径不能为空")
  28:             return False
  29:         
  30:         if check_exists and not os.path.exists(file_path):
  31:             logging.error(f"文件不存在: {file_path}")
  32:             return False
  33:         
  34:         # 检查文件扩展名
  35:         valid_extensions = ['.tif', '.tiff', '.img', '.nc', '.hdf', '.h5']
  36:         ext = os.path.splitext(file_path)[1].lower()
  37:         if check_exists and ext not in valid_extensions:
  38:             logging.warning(f"可能不支持的文件格式: {ext}")
  39:         
  40:         return True
  41:     
  42:     @staticmethod
  43:     def validate_grid_level(level: int) -> bool:
  44:         """
  45:         验证网格级别
  46:         
  47:         Args:
  48:             level: 网格级别
  49:             
  50:         Returns:
  51:             是否有效
  52:         """
  53:         if not isinstance(level, int):
  54:             logging.error("网格级别必须是整数")
  55:             return False
  56:         
  57:         if level < 1 or level > 10:
  58:             logging.error(f"网格级别必须在1-10之间，当前值: {level}")
  59:             return False
  60:         
  61:         return True
  62:     
  63:     @staticmethod
  64:     def validate_coordinates(lat: float, lon: float) -> bool:
  65:         """
  66:         验证坐标
  67:         
  68:         Args:
  69:             lat: 纬度
  70:             lon: 经度
  71:             
  72:         Returns:
  73:             是否有效
  74:         """
  75:         if not isinstance(lat, (int, float)) or not isinstance(lon, (int, float)):
  76:             logging.error("坐标必须是数值类型")
  77:             return False
  78:         
  79:         if lat < -90 or lat > 90:
  80:             logging.error(f"纬度必须在-90到90之间，当前值: {lat}")
  81:             return False
  82:         
  83:         if lon < -180 or lon > 180:
  84:             logging.error(f"经度必须在-180到180之间，当前值: {lon}")
  85:             return False
  86:         
  87:         return True
  88:     
  89:     @staticmethod
  90:     def validate_bounds(min_lon: float, min_lat: float, 
  91:                        max_lon: float, max_lat: float) -> bool:
  92:         """
  93:         验证边界坐标
  94:         
  95:         Args:
  96:             min_lon, min_lat, max_lon, max_lat: 边界坐标
  97:             
  98:         Returns:
  99:             是否有效
 100:         """
 101:         # 验证各个坐标
 102:         if not all(DataValidator.validate_coordinates(lat, lon) 
 103:                   for lat, lon in [(min_lat, min_lon), (max_lat, max_lon)]):
 104:             return False
 105:         
 106:         # 验证边界逻辑
 107:         if min_lon >= max_lon:
 108:             logging.error(f"最小经度({min_lon})必须小于最大经度({max_lon})")
 109:             return False
 110:         
 111:         if min_lat >= max_lat:
 112:             logging.error(f"最小纬度({min_lat})必须小于最大纬度({max_lat})")
 113:             return False
 114:         
 115:         return True
 116:     
 117:     @staticmethod
 118:     def validate_array(array: np.ndarray, min_shape: Tuple[int, int] = (1, 1)) -> bool:
 119:         """
 120:         验证数组
 121:         
 122:         Args:
 123:             array: numpy数组
 124:             min_shape: 最小形状
 125:             
 126:         Returns:
 127:             是否有效
 128:         """
 129:         if not isinstance(array, np.ndarray):
 130:             logging.error("输入必须是numpy数组")
 131:             return False
 132:         
 133:         if array.ndim != 2:
 134:             logging.error(f"数组必须是二维的，当前维度: {array.ndim}")
 135:             return False
 136:         
 137:         if array.shape[0] < min_shape[0] or array.shape[1] < min_shape[1]:
 138:             logging.error(f"数组形状{array.shape}小于最小要求{min_shape}")
 139:             return False
 140:         
 141:         if array.size == 0:
 142:             logging.error("数组不能为空")
 143:             return False
 144:         
 145:         return True
 146:     
 147:     @staticmethod
 148:     def validate_window_size(window_size: int, max_size: int = 100) -> bool:
 149:         """
 150:         验证窗口大小
 151:         
 152:         Args:
 153:             window_size: 窗口大小
 154:             max_size: 最大窗口大小
 155:             
 156:         Returns:
 157:             是否有效
 158:         """
 159:         if not isinstance(window_size, int):
 160:             logging.error("窗口大小必须是整数")
 161:             return False
 162:         
 163:         if window_size < 1:
 164:             logging.error(f"窗口大小必须大于0，当前值: {window_size}")
 165:             return False
 166:         
 167:         if window_size > max_size:
 168:             logging.error(f"窗口大小不能超过{max_size}，当前值: {window_size}")
 169:             return False
 170:         
 171:         if window_size % 2 == 0:
 172:             logging.warning(f"建议使用奇数窗口大小，当前值: {window_size}")
 173:         
 174:         return True
 175:     
 176:     @staticmethod
 177:     def validate_aggregation_method(method: str) -> bool:
 178:         """
 179:         验证聚合方法
 180:         
 181:         Args:
 182:             method: 聚合方法
 183:             
 184:         Returns:
 185:             是否有效
 186:         """
 187:         from core.aggregation import AggregationMethod
 188:         
 189:         if not isinstance(method, str):
 190:             logging.error("聚合方法必须是字符串")
 191:             return False
 192:         
 193:         valid_methods = [m.value for m in AggregationMethod]
 194:         if method not in valid_methods:
 195:             logging.error(f"不支持的聚合方法: {method}，支持的方法: {valid_methods}")
 196:             return False
 197:         
 198:         return True
 199:     
 200:     @staticmethod
 201:     def validate_output_path(output_path: str, create_dir: bool = True) -> bool:
 202:         """
 203:         验证输出路径
 204:         
 205:         Args:
 206:             output_path: 输出路径
 207:             create_dir: 是否创建目录
 208:             
 209:         Returns:
 210:             是否有效
 211:         """
 212:         if not output_path or not isinstance(output_path, str):
 213:             logging.error("输出路径不能为空")
 214:             return False
 215:         
 216:         # 检查目录
 217:         output_dir = os.path.dirname(output_path)
 218:         if output_dir and not os.path.exists(output_dir):
 219:             if create_dir:
 220:                 try:
 221:                     os.makedirs(output_dir, exist_ok=True)
 222:                     logging.info(f"创建输出目录: {output_dir}")
 223:                 except Exception as e:
 224:                     logging.error(f"无法创建输出目录 {output_dir}: {str(e)}")
 225:                     return False
 226:             else:
 227:                 logging.error(f"输出目录不存在: {output_dir}")
 228:                 return False
 229:         
 230:         # 检查文件权限
 231:         if os.path.exists(output_path):
 232:             if not os.access(output_path, os.W_OK):
 233:                 logging.error(f"没有写入权限: {output_path}")
 234:                 return False
 235:         else:
 236:             # 检查目录写入权限
 237:             if output_dir and not os.access(output_dir, os.W_OK):
 238:                 logging.error(f"没有目录写入权限: {output_dir}")
 239:                 return False
 240:         
 241:         return True
 242:     
 243:     @staticmethod
 244:     def validate_memory_usage(array_shape: Tuple[int, int], 
 245:                             dtype: np.dtype = np.float64,
 246:                             max_memory_gb: float = 4.0) -> bool:
 247:         """
 248:         验证内存使用量
 249:         
 250:         Args:
 251:             array_shape: 数组形状
 252:             dtype: 数据类型
 253:             max_memory_gb: 最大内存使用量（GB）
 254:             
 255:         Returns:
 256:             是否在限制内
 257:         """
 258:         # 计算内存使用量
 259:         element_size = np.dtype(dtype).itemsize
 260:         total_elements = np.prod(array_shape)
 261:         memory_bytes = total_elements * element_size
 262:         memory_gb = memory_bytes / (1024**3)
 263:         
 264:         if memory_gb > max_memory_gb:
 265:             logging.warning(f"预计内存使用量 {memory_gb:.2f}GB 超过限制 {max_memory_gb}GB")
 266:             return False
 267:         
 268:         logging.info(f"预计内存使用量: {memory_gb:.2f}GB")
 269:         return True
 270:     
 271:     @staticmethod
 272:     def validate_processing_parameters(params: Dict[str, Any]) -> Tuple[bool, List[str]]:
 273:         """
 274:         验证处理参数
 275:         
 276:         Args:
 277:             params: 参数字典
 278:             
 279:         Returns:
 280:             (是否有效, 错误信息列表)
 281:         """
 282:         errors = []
 283:         
 284:         # 必需参数
 285:         required_params = ['input_file', 'output_file', 'grid_level']
 286:         for param in required_params:
 287:             if param not in params:
 288:                 errors.append(f"缺少必需参数: {param}")
 289:         
 290:         if errors:
 291:             return False, errors
 292:         
 293:         # 验证各个参数
 294:         if not DataValidator.validate_file_path(params['input_file']):
 295:             errors.append("输入文件路径无效")
 296:         
 297:         if not DataValidator.validate_output_path(params['output_file']):
 298:             errors.append("输出文件路径无效")
 299:         
 300:         if not DataValidator.validate_grid_level(params['grid_level']):
 301:             errors.append("网格级别无效")
 302:         
 303:         # 可选参数验证
 304:         if 'aggregation_method' in params:
 305:             if not DataValidator.validate_aggregation_method(params['aggregation_method']):
 306:                 errors.append("聚合方法无效")
 307:         
 308:         if 'window_size' in params:
 309:             if not DataValidator.validate_window_size(params['window_size']):
 310:                 errors.append("窗口大小无效")
 311:         
 312:         return len(errors) == 0, errors
 313: 


================================================================================
文件 10: data\raster_io.py
================================================================================

   1: """
   2: 栅格数据读写模块
   3: 使用rasterio替代ArcPy
   4: """
   5: 
   6: import os
   7: import numpy as np
   8: import rasterio
   9: from rasterio.transform import from_bounds
  10: from rasterio.enums import Resampling
  11: from rasterio.warp import calculate_default_transform, reproject
  12: from typing import Tuple, Optional, Dict, Any, Union
  13: import logging
  14: 
  15: 
  16: class RasterReader:
  17:     """栅格数据读取器"""
  18:     
  19:     def __init__(self, file_path: str):
  20:         """
  21:         初始化读取器
  22:         
  23:         Args:
  24:             file_path: 栅格文件路径
  25:         """
  26:         self.file_path = file_path
  27:         self._dataset = None
  28:         self._array = None
  29:         self._transform = None
  30:         self._crs = None
  31:         self._nodata = None
  32:         
  33:         if not os.path.exists(file_path):
  34:             raise FileNotFoundError(f"文件不存在: {file_path}")
  35:     
  36:     def __enter__(self):
  37:         """上下文管理器入口"""
  38:         self.open()
  39:         return self
  40:     
  41:     def __exit__(self, exc_type, exc_val, exc_tb):
  42:         """上下文管理器出口"""
  43:         self.close()
  44:     
  45:     def open(self):
  46:         """打开栅格文件"""
  47:         try:
  48:             self._dataset = rasterio.open(self.file_path)
  49:             self._transform = self._dataset.transform
  50:             self._crs = self._dataset.crs
  51:             self._nodata = self._dataset.nodata
  52:             logging.info(f"成功打开栅格文件: {self.file_path}")
  53:         except Exception as e:
  54:             raise RuntimeError(f"无法打开栅格文件 {self.file_path}: {str(e)}")
  55:     
  56:     def close(self):
  57:         """关闭栅格文件"""
  58:         if self._dataset:
  59:             self._dataset.close()
  60:             self._dataset = None
  61:     
  62:     def read_array(self, band: int = 1, window: Optional[rasterio.windows.Window] = None,
  63:                    masked: bool = True) -> np.ndarray:
  64:         """
  65:         读取栅格数据为numpy数组
  66:         
  67:         Args:
  68:             band: 波段号（从1开始）
  69:             window: 读取窗口
  70:             masked: 是否返回掩码数组
  71:             
  72:         Returns:
  73:             numpy数组
  74:         """
  75:         if not self._dataset:
  76:             self.open()
  77:         
  78:         try:
  79:             array = self._dataset.read(band, window=window, masked=masked)
  80:             
  81:             # 处理无效值
  82:             if not masked and self._nodata is not None:
  83:                 array = np.where(array == self._nodata, np.nan, array)
  84:             
  85:             self._array = array
  86:             logging.info(f"成功读取数组，形状: {array.shape}")
  87:             return array
  88:             
  89:         except Exception as e:
  90:             raise RuntimeError(f"读取栅格数据失败: {str(e)}")
  91:     
  92:     def get_bounds(self) -> Tuple[float, float, float, float]:
  93:         """
  94:         获取栅格边界
  95:         
  96:         Returns:
  97:             (min_x, min_y, max_x, max_y)
  98:         """
  99:         if not self._dataset:
 100:             self.open()
 101:         
 102:         return self._dataset.bounds
 103:     
 104:     def get_transform(self) -> rasterio.transform.Affine:
 105:         """获取仿射变换参数"""
 106:         if not self._dataset:
 107:             self.open()
 108:         
 109:         return self._transform
 110:     
 111:     def get_crs(self):
 112:         """获取坐标参考系统"""
 113:         if not self._dataset:
 114:             self.open()
 115:         
 116:         return self._crs
 117:     
 118:     def get_shape(self) -> Tuple[int, int]:
 119:         """获取栅格形状"""
 120:         if not self._dataset:
 121:             self.open()
 122:         
 123:         return self._dataset.height, self._dataset.width
 124:     
 125:     def get_nodata_value(self) -> Optional[float]:
 126:         """获取无效值"""
 127:         if not self._dataset:
 128:             self.open()
 129:         
 130:         return self._nodata
 131:     
 132:     def get_metadata(self) -> Dict[str, Any]:
 133:         """获取元数据"""
 134:         if not self._dataset:
 135:             self.open()
 136:         
 137:         return {
 138:             'driver': self._dataset.driver,
 139:             'dtype': self._dataset.dtypes[0],
 140:             'nodata': self._dataset.nodata,
 141:             'width': self._dataset.width,
 142:             'height': self._dataset.height,
 143:             'count': self._dataset.count,
 144:             'crs': self._dataset.crs,
 145:             'transform': self._dataset.transform,
 146:             'bounds': self._dataset.bounds
 147:         }
 148:     
 149:     def read_window(self, row_start: int, row_stop: int, 
 150:                    col_start: int, col_stop: int, band: int = 1) -> np.ndarray:
 151:         """
 152:         读取指定窗口的数据
 153:         
 154:         Args:
 155:             row_start, row_stop: 行范围
 156:             col_start, col_stop: 列范围
 157:             band: 波段号
 158:             
 159:         Returns:
 160:             窗口数据
 161:         """
 162:         window = rasterio.windows.Window(col_start, row_start, 
 163:                                        col_stop - col_start, 
 164:                                        row_stop - row_start)
 165:         return self.read_array(band=band, window=window)
 166:     
 167:     def sample_points(self, coordinates: list, band: int = 1) -> list:
 168:         """
 169:         在指定坐标点采样
 170:         
 171:         Args:
 172:             coordinates: 坐标点列表 [(x, y), ...]
 173:             band: 波段号
 174:             
 175:         Returns:
 176:             采样值列表
 177:         """
 178:         if not self._dataset:
 179:             self.open()
 180:         
 181:         try:
 182:             samples = list(self._dataset.sample(coordinates, indexes=band))
 183:             return [sample[0] for sample in samples]
 184:         except Exception as e:
 185:             raise RuntimeError(f"坐标采样失败: {str(e)}")
 186: 
 187: 
 188: class RasterWriter:
 189:     """栅格数据写入器"""
 190:     
 191:     def __init__(self, file_path: str, array: np.ndarray, 
 192:                  transform: rasterio.transform.Affine,
 193:                  crs: Optional[Any] = None,
 194:                  nodata: Optional[float] = None,
 195:                  dtype: Optional[str] = None,
 196:                  compress: str = 'lzw'):
 197:         """
 198:         初始化写入器
 199:         
 200:         Args:
 201:             file_path: 输出文件路径
 202:             array: 要写入的数组
 203:             transform: 仿射变换参数
 204:             crs: 坐标参考系统
 205:             nodata: 无效值
 206:             dtype: 数据类型
 207:             compress: 压缩方式
 208:         """
 209:         self.file_path = file_path
 210:         self.array = array
 211:         self.transform = transform
 212:         self.crs = crs
 213:         self.nodata = nodata
 214:         self.dtype = dtype or array.dtype
 215:         self.compress = compress
 216:         
 217:         # 确保输出目录存在
 218:         os.makedirs(os.path.dirname(file_path), exist_ok=True)
 219:     
 220:     def write(self, create_overview: bool = True) -> bool:
 221:         """
 222:         写入栅格文件
 223:         
 224:         Args:
 225:             create_overview: 是否创建概览图
 226:             
 227:         Returns:
 228:             是否成功
 229:         """
 230:         try:
 231:             # 确定数组维度
 232:             if self.array.ndim == 2:
 233:                 height, width = self.array.shape
 234:                 count = 1
 235:                 array_to_write = self.array[np.newaxis, :, :]  # 添加波段维度
 236:             elif self.array.ndim == 3:
 237:                 count, height, width = self.array.shape
 238:                 array_to_write = self.array
 239:             else:
 240:                 raise ValueError(f"不支持的数组维度: {self.array.ndim}")
 241:             
 242:             # 设置写入参数
 243:             profile = {
 244:                 'driver': 'GTiff',
 245:                 'dtype': self.dtype,
 246:                 'nodata': self.nodata,
 247:                 'width': width,
 248:                 'height': height,
 249:                 'count': count,
 250:                 'crs': self.crs,
 251:                 'transform': self.transform,
 252:                 'compress': self.compress,
 253:                 'tiled': True,
 254:                 'blockxsize': 512,
 255:                 'blockysize': 512
 256:             }
 257:             
 258:             # 写入文件
 259:             with rasterio.open(self.file_path, 'w', **profile) as dst:
 260:                 dst.write(array_to_write)
 261:                 
 262:                 # 创建概览图
 263:                 if create_overview:
 264:                     factors = [2, 4, 8, 16]
 265:                     dst.build_overviews(factors, Resampling.average)
 266:                     dst.update_tags(ns='rio_overview', resampling='average')
 267:             
 268:             logging.info(f"成功写入栅格文件: {self.file_path}")
 269:             return True
 270:             
 271:         except Exception as e:
 272:             logging.error(f"写入栅格文件失败: {str(e)}")
 273:             return False
 274:     
 275:     @staticmethod
 276:     def create_from_bounds(min_x: float, min_y: float, max_x: float, max_y: float,
 277:                           width: int, height: int, crs: Optional[Any] = None) -> rasterio.transform.Affine:
 278:         """
 279:         根据边界创建仿射变换参数
 280:         
 281:         Args:
 282:             min_x, min_y, max_x, max_y: 边界坐标
 283:             width, height: 栅格尺寸
 284:             crs: 坐标参考系统
 285:             
 286:         Returns:
 287:             仿射变换参数
 288:         """
 289:         return from_bounds(min_x, min_y, max_x, max_y, width, height)
 290:     
 291:     @staticmethod
 292:     def array_to_raster(array: np.ndarray, file_path: str,
 293:                        min_x: float, min_y: float, max_x: float, max_y: float,
 294:                        crs: Optional[Any] = None, nodata: Optional[float] = None) -> bool:
 295:         """
 296:         便捷方法：将数组直接写入栅格文件
 297:         
 298:         Args:
 299:             array: 数组
 300:             file_path: 输出路径
 301:             min_x, min_y, max_x, max_y: 边界
 302:             crs: 坐标系
 303:             nodata: 无效值
 304:             
 305:         Returns:
 306:             是否成功
 307:         """
 308:         height, width = array.shape
 309:         transform = RasterWriter.create_from_bounds(min_x, min_y, max_x, max_y, width, height)
 310:         
 311:         writer = RasterWriter(file_path, array, transform, crs, nodata)
 312:         return writer.write()
 313: 


================================================================================
文件 11: data\vector_io.py
================================================================================

   1: """
   2: 矢量数据写入模块
   3: """
   4: 
   5: import os
   6: import numpy as np
   7: import fiona
   8: from fiona.crs import from_epsg
   9: from shapely.geometry import Polygon, mapping
  10: from typing import List, Dict, Any, Optional, Tuple
  11: import logging
  12: 
  13: 
  14: class VectorWriter:
  15:     """矢量数据写入器"""
  16:     
  17:     def __init__(self, file_path: str, crs: Optional[Any] = None):
  18:         """
  19:         初始化写入器
  20:         
  21:         Args:
  22:             file_path: 输出文件路径
  23:             crs: 坐标参考系统
  24:         """
  25:         self.file_path = file_path
  26:         self.crs = crs or from_epsg(4326)  # 默认使用WGS84
  27:         
  28:         # 确保输出目录存在
  29:         os.makedirs(os.path.dirname(file_path), exist_ok=True)
  30:     
  31:     def write_grid_polygons(self, grid_data: np.ndarray,
  32:                            min_lon: float, min_lat: float,
  33:                            max_lon: float, max_lat: float,
  34:                            grid_level: int,
  35:                            grid_calculator,
  36:                            additional_fields: Optional[Dict[str, Any]] = None,
  37:                            progress_callback=None) -> bool:
  38:         """
  39:         将网格数据写入矢量文件
  40:         
  41:         Args:
  42:             grid_data: 网格数据数组
  43:             min_lon, min_lat, max_lon, max_lat: 边界坐标
  44:             grid_level: 网格级别
  45:             grid_calculator: 网格计算器实例
  46:             additional_fields: 额外字段
  47:             
  48:         Returns:
  49:             是否成功
  50:         """
  51:         try:
  52:             # 获取网格尺寸
  53:             lon_size, lat_size = grid_calculator.get_grid_size(grid_level)
  54:             rows, cols = grid_data.shape
  55: 
  56:             # 计算有效要素数量（用于进度显示）
  57:             valid_count = np.sum(~np.isnan(grid_data) & (grid_data != 0))
  58:             if progress_callback:
  59:                 progress_callback(90, "开始写入矢量文件", f"预计要素数量: {valid_count}")
  60: 
  61:             processed_count = 0
  62:             
  63:             # 定义schema
  64:             schema = {
  65:                 'geometry': 'Polygon',
  66:                 'properties': {
  67:                     'grid_code': 'str',
  68:                     'grid_level': 'int',
  69:                     'value': 'float',
  70:                     'row': 'int',
  71:                     'col': 'int',
  72:                     'center_lon': 'float',
  73:                     'center_lat': 'float',
  74:                     'area_sqm': 'float'
  75:                 }
  76:             }
  77:             
  78:             # 添加额外字段
  79:             if additional_fields:
  80:                 schema['properties'].update(additional_fields)
  81:             
  82:             # 写入文件
  83:             with fiona.open(self.file_path, 'w', 
  84:                           driver='ESRI Shapefile',
  85:                           crs=self.crs,
  86:                           schema=schema) as output:
  87:                 
  88:                 for i in range(rows):
  89:                     for j in range(cols):
  90:                         value = grid_data[i, j]
  91: 
  92:                         # 跳过无效值
  93:                         if np.isnan(value) or value == 0:
  94:                             continue
  95: 
  96:                         processed_count += 1
  97:                         
  98:                         # 计算网格中心坐标
  99:                         center_lon = min_lon + (j + 0.5) * lon_size
 100:                         center_lat = min_lat + (i + 0.5) * lat_size
 101:                         
 102:                         # 计算网格边界
 103:                         grid_min_lon = min_lon + j * lon_size
 104:                         grid_max_lon = min_lon + (j + 1) * lon_size
 105:                         grid_min_lat = min_lat + i * lat_size
 106:                         grid_max_lat = min_lat + (i + 1) * lat_size
 107:                         
 108:                         # 创建多边形
 109:                         polygon = Polygon([
 110:                             (grid_min_lon, grid_min_lat),
 111:                             (grid_max_lon, grid_min_lat),
 112:                             (grid_max_lon, grid_max_lat),
 113:                             (grid_min_lon, grid_max_lat),
 114:                             (grid_min_lon, grid_min_lat)
 115:                         ])
 116:                         
 117:                         # 计算网格编码
 118:                         grid_code = grid_calculator.calculate_grid_code(
 119:                             center_lat, center_lon, grid_level)
 120:                         
 121:                         # 计算面积
 122:                         from core.coordinate_utils import CoordinateUtils
 123:                         area = CoordinateUtils.calculate_grid_area(center_lat, grid_level)
 124:                         
 125:                         # 创建要素
 126:                         feature = {
 127:                             'geometry': mapping(polygon),
 128:                             'properties': {
 129:                                 'grid_code': grid_code,
 130:                                 'grid_level': grid_level,
 131:                                 'value': float(value),
 132:                                 'row': i,
 133:                                 'col': j,
 134:                                 'center_lon': center_lon,
 135:                                 'center_lat': center_lat,
 136:                                 'area_sqm': area
 137:                             }
 138:                         }
 139:                         
 140:                         output.write(feature)
 141: 
 142:                         # 更新进度（每100个要素更新一次）
 143:                         if progress_callback and processed_count % 100 == 0:
 144:                             progress = 90 + int((processed_count / valid_count) * 5)  # 90-95%
 145:                             progress_callback(progress, "写入矢量要素", f"已处理: {processed_count}/{valid_count}")
 146:             
 147:             logging.info(f"成功写入矢量文件: {self.file_path}")
 148:             return True
 149:             
 150:         except Exception as e:
 151:             logging.error(f"写入矢量文件失败: {str(e)}")
 152:             return False
 153:     
 154:     def write_polygons_from_coordinates(self, polygons_data: List[Dict[str, Any]]) -> bool:
 155:         """
 156:         从坐标数据写入多边形
 157:         
 158:         Args:
 159:             polygons_data: 多边形数据列表，每个元素包含 'coordinates' 和 'properties'
 160:             
 161:         Returns:
 162:             是否成功
 163:         """
 164:         try:
 165:             if not polygons_data:
 166:                 logging.warning("没有数据要写入")
 167:                 return False
 168:             
 169:             # 从第一个多边形推断schema
 170:             first_polygon = polygons_data[0]
 171:             properties = first_polygon.get('properties', {})
 172:             
 173:             schema = {
 174:                 'geometry': 'Polygon',
 175:                 'properties': {}
 176:             }
 177:             
 178:             # 推断属性类型
 179:             for key, value in properties.items():
 180:                 if isinstance(value, str):
 181:                     schema['properties'][key] = 'str'
 182:                 elif isinstance(value, int):
 183:                     schema['properties'][key] = 'int'
 184:                 elif isinstance(value, float):
 185:                     schema['properties'][key] = 'float'
 186:                 else:
 187:                     schema['properties'][key] = 'str'
 188:             
 189:             # 写入文件
 190:             with fiona.open(self.file_path, 'w',
 191:                           driver='ESRI Shapefile',
 192:                           crs=self.crs,
 193:                           schema=schema) as output:
 194:                 
 195:                 for polygon_data in polygons_data:
 196:                     coordinates = polygon_data['coordinates']
 197:                     properties = polygon_data.get('properties', {})
 198:                     
 199:                     # 创建多边形
 200:                     polygon = Polygon(coordinates)
 201:                     
 202:                     # 创建要素
 203:                     feature = {
 204:                         'geometry': mapping(polygon),
 205:                         'properties': properties
 206:                     }
 207:                     
 208:                     output.write(feature)
 209:             
 210:             logging.info(f"成功写入 {len(polygons_data)} 个多边形到: {self.file_path}")
 211:             return True
 212:             
 213:         except Exception as e:
 214:             logging.error(f"写入多边形失败: {str(e)}")
 215:             return False
 216:     
 217:     def raster_to_polygons(self, raster_array: np.ndarray,
 218:                           transform: Any,
 219:                           mask_value: Optional[float] = None,
 220:                           progress_callback=None) -> bool:
 221:         """
 222:         将栅格转换为多边形
 223:         
 224:         Args:
 225:             raster_array: 栅格数组
 226:             transform: 仿射变换参数
 227:             mask_value: 掩码值（不转换的值）
 228:             
 229:         Returns:
 230:             是否成功
 231:         """
 232:         try:
 233:             import rasterio.features
 234: 
 235:             if progress_callback:
 236:                 progress_callback(90, "开始提取多边形", "分析栅格数据...")
 237: 
 238:             # 创建掩码
 239:             if mask_value is not None:
 240:                 mask = raster_array != mask_value
 241:             else:
 242:                 mask = np.ones_like(raster_array, dtype=bool)
 243: 
 244:             if progress_callback:
 245:                 progress_callback(92, "提取多边形", "正在矢量化栅格数据...")
 246: 
 247:             # 提取多边形
 248:             shapes = list(rasterio.features.shapes(
 249:                 raster_array.astype(np.int32),
 250:                 mask=mask,
 251:                 transform=transform
 252:             ))
 253: 
 254:             total_shapes = len(shapes)
 255:             if progress_callback:
 256:                 progress_callback(93, "写入多边形", f"共提取到 {total_shapes} 个多边形")
 257:             
 258:             # 定义schema
 259:             schema = {
 260:                 'geometry': 'Polygon',
 261:                 'properties': {
 262:                     'value': 'int'
 263:                 }
 264:             }
 265:             
 266:             # 写入文件
 267:             with fiona.open(self.file_path, 'w',
 268:                           driver='ESRI Shapefile',
 269:                           crs=self.crs,
 270:                           schema=schema) as output:
 271: 
 272:                 processed = 0
 273:                 for geom, value in shapes:
 274:                     if value != mask_value:  # 跳过掩码值
 275:                         feature = {
 276:                             'geometry': geom,
 277:                             'properties': {
 278:                                 'value': int(value)
 279:                             }
 280:                         }
 281:                         output.write(feature)
 282:                         processed += 1
 283: 
 284:                         # 更新进度（每50个多边形更新一次）
 285:                         if progress_callback and processed % 50 == 0:
 286:                             progress = 93 + int((processed / total_shapes) * 2)  # 93-95%
 287:                             progress_callback(progress, "写入多边形", f"已写入: {processed}/{total_shapes}")
 288:             
 289:             logging.info(f"成功将栅格转换为多边形: {self.file_path}")
 290:             return True
 291:             
 292:         except Exception as e:
 293:             logging.error(f"栅格转多边形失败: {str(e)}")
 294:             return False
 295:     
 296:     @staticmethod
 297:     def create_grid_shapefile(output_path: str, 
 298:                             min_lon: float, min_lat: float,
 299:                             max_lon: float, max_lat: float,
 300:                             grid_level: int,
 301:                             grid_calculator,
 302:                             crs: Optional[Any] = None) -> bool:
 303:         """
 304:         创建网格矢量文件（不包含数据值）
 305:         
 306:         Args:
 307:             output_path: 输出路径
 308:             min_lon, min_lat, max_lon, max_lat: 边界
 309:             grid_level: 网格级别
 310:             grid_calculator: 网格计算器
 311:             crs: 坐标系
 312:             
 313:         Returns:
 314:             是否成功
 315:         """
 316:         writer = VectorWriter(output_path, crs)
 317:         
 318:         # 获取网格尺寸
 319:         lon_size, lat_size = grid_calculator.get_grid_size(grid_level)
 320:         
 321:         # 计算网格数量
 322:         cols = int(np.ceil((max_lon - min_lon) / lon_size))
 323:         rows = int(np.ceil((max_lat - min_lat) / lat_size))
 324:         
 325:         # 创建空的网格数据（全部为1，表示有效网格）
 326:         grid_data = np.ones((rows, cols))
 327:         
 328:         return writer.write_grid_polygons(
 329:             grid_data, min_lon, min_lat, max_lon, max_lat,
 330:             grid_level, grid_calculator
 331:         )
 332: 


================================================================================
文件 12: gui\__init__.py
================================================================================

   1: """
   2: 图形界面模块
   3: """
   4: 
   5: from .main_window import MainWindow
   6: from .parameter_panel import ParameterPanel
   7: from .progress_dialog import ProgressDialog
   8: 
   9: __all__ = [
  10:     'MainWindow',
  11:     'ParameterPanel', 
  12:     'Progress Dialog'
  13: ]
  14: 


================================================================================
文件 13: gui\main_window.py
================================================================================

   1: """
   2: 主窗口界面
   3: """
   4: 
   5: import tkinter as tk
   6: from tkinter import ttk, filedialog, messagebox
   7: import threading
   8: import os
   9: from typing import Optional
  10: 
  11: from .parameter_panel import ParameterPanel
  12: from .progress_dialog import ProgressDialog
  13: from .window_utils import configure_window_properties
  14: import sys
  15: import os
  16: sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
  17: 
  18: from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
  19: from core.aggregation import AggregationMethod
  20: 
  21: 
  22: class MainWindow:
  23:     """主窗口类"""
  24:     
  25:     def __init__(self):
  26:         """初始化主窗口"""
  27:         self.root = tk.Tk()
  28: 
  29:         # 配置窗口属性（居中显示）
  30:         configure_window_properties(
  31:             window=self.root,
  32:             title="北斗网格转换工具 v2.0",
  33:             width=750,
  34:             height= 780,
  35:             min_width=750,
  36:             min_height=780,
  37:             resizable=True,
  38:             center=True,
  39:             icon_path="icon.ico"
  40:         )
  41: 
  42:         # 初始化变量
  43:         self.processor: Optional[BeidouGridProcessor] = None
  44:         self.progress_dialog: Optional[ProgressDialog] = None
  45:         self.current_processor: Optional[BeidouGridProcessor] = None
  46:         
  47:         # 创建界面
  48:         self._create_widgets()
  49:         self._setup_layout()
  50:         self._bind_events()
  51:         
  52:         # 设置默认值
  53:         self._set_default_values()
  54:     
  55:     def _create_widgets(self):
  56:         """创建界面组件"""
  57:         # 创建主框架
  58:         self.main_frame = ttk.Frame(self.root, padding="15")
  59:         
  60:         # 标题
  61:         self.title_label = ttk.Label(
  62:             self.main_frame, 
  63:             text="北斗网格转换工具", 
  64:             font=("Arial", 16, "bold")
  65:         )
  66:         
  67:         # 输入文件选择
  68:         self.input_frame = ttk.LabelFrame(self.main_frame, text="输入文件", padding="5")
  69:         self.input_path_var = tk.StringVar()
  70:         self.input_entry = ttk.Entry(self.input_frame, textvariable=self.input_path_var, width=60)
  71:         self.input_browse_btn = ttk.Button(self.input_frame, text="浏览...", command=self._browse_input_file)
  72:         
  73:         # 输出设置
  74:         self.output_frame = ttk.LabelFrame(self.main_frame, text="输出设置", padding="5")
  75:         
  76:         # 输出目录
  77:         self.output_dir_var = tk.StringVar()
  78:         self.output_dir_entry = ttk.Entry(self.output_frame, textvariable=self.output_dir_var, width=50)
  79:         self.output_dir_btn = ttk.Button(self.output_frame, text="选择目录", command=self._browse_output_dir)
  80:         
  81:         # 输出文件名
  82:         self.output_name_var = tk.StringVar(value="output")
  83:         self.output_name_entry = ttk.Entry(self.output_frame, textvariable=self.output_name_var, width=20)
  84:         
  85:         # 输出格式选择变量
  86:         self.output_raster_var = tk.BooleanVar(value=True)
  87:         self.output_vector_var = tk.BooleanVar(value=True)
  88:         
  89:         # 参数面板
  90:         self.param_panel = ParameterPanel(self.main_frame)
  91:         
  92:         # 控制按钮
  93:         self.control_frame = ttk.Frame(self.main_frame)
  94:         self.process_btn = ttk.Button(
  95:             self.control_frame,
  96:             text="开始处理",
  97:             command=self._start_processing
  98:         )
  99:         self.reset_btn = ttk.Button(self.control_frame, text="重置", command=self._reset_form)
 100:         self.exit_btn = ttk.Button(self.control_frame, text="退出", command=self._exit_application)
 101:         
 102:         # 状态栏
 103:         self.status_frame = ttk.Frame(self.main_frame)
 104:         self.status_var = tk.StringVar(value="就绪")
 105:         self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
 106:         
 107:         # 结果显示区域（隐藏，由进度条替代）
 108:         # self.result_frame = ttk.LabelFrame(self.main_frame, text="处理结果", padding="5")
 109:         # self.result_text = tk.Text(self.result_frame, height=8, width=80, wrap=tk.WORD)
 110:         # self.result_scroll = ttk.Scrollbar(self.result_frame, orient="vertical", command=self.result_text.yview)
 111:         # self.result_text.configure(yscrollcommand=self.result_scroll.set)
 112:     
 113:     def _setup_layout(self):
 114:         """设置布局"""
 115:         # 主框架
 116:         self.main_frame.grid(row=0, column=0, sticky="nsew")
 117:         self.root.grid_rowconfigure(0, weight=1)
 118:         self.root.grid_columnconfigure(0, weight=1)
 119:         
 120:         # 标题
 121:         self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
 122:         
 123:         # 输入文件
 124:         self.input_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
 125:         self.input_frame.grid_columnconfigure(0, weight=1)
 126:         
 127:         ttk.Label(self.input_frame, text="栅格文件:").grid(row=0, column=0, sticky="w")
 128:         self.input_entry.grid(row=1, column=0, sticky="ew", padx=(0, 5))
 129:         self.input_browse_btn.grid(row=1, column=1)
 130:         
 131:         # 输出设置
 132:         self.output_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
 133:         self.output_frame.grid_columnconfigure(0, weight=1)
 134:         
 135:         ttk.Label(self.output_frame, text="输出目录:").grid(row=0, column=0, sticky="w")
 136:         self.output_dir_entry.grid(row=1, column=0, sticky="ew", padx=(0, 5))
 137:         self.output_dir_btn.grid(row=1, column=1)
 138:         
 139:         ttk.Label(self.output_frame, text="文件名:").grid(row=2, column=0, sticky="w", pady=(10, 0))
 140:         self.output_name_entry.grid(row=3, column=0, sticky="w")
 141:         
 142:         ttk.Label(self.output_frame, text="输出格式:").grid(row=4, column=0, sticky="w", pady=(10, 0))
 143:         format_frame = ttk.Frame(self.output_frame)
 144:         format_frame.grid(row=5, column=0, sticky="ew", columnspan=2)
 145: 
 146:         # 将复选框放置在format_frame中
 147:         self.raster_check = ttk.Checkbutton(format_frame, text="栅格文件(.tif)", variable=self.output_raster_var)
 148:         self.vector_check = ttk.Checkbutton(format_frame, text="矢量文件(.shp)", variable=self.output_vector_var)
 149:         self.raster_check.grid(row=0, column=0, padx=(0, 20))
 150:         self.vector_check.grid(row=0, column=1)
 151: 
 152: 
 153:         
 154:         # 参数面板
 155:         self.param_panel.frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(0, 10))
 156:         
 157:         # 控制按钮
 158:         self.control_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
 159:         self.process_btn.grid(row=0, column=0, padx=(0, 10))
 160:         self.reset_btn.grid(row=0, column=1, padx=(0, 10))
 161:         self.exit_btn.grid(row=0, column=2)
 162:         
 163:         # 结果显示（隐藏）
 164:         # self.result_frame.grid(row=5, column=0, columnspan=2, sticky="nsew", pady=(0, 10))
 165:         # self.result_frame.grid_rowconfigure(0, weight=1)
 166:         # self.result_frame.grid_columnconfigure(0, weight=1)
 167:         #
 168:         # self.result_text.grid(row=0, column=0, sticky="nsew")
 169:         # self.result_scroll.grid(row=0, column=1, sticky="ns")
 170:         
 171:         # 状态栏
 172:         self.status_frame.grid(row=5, column=0, columnspan=2, sticky="ew")
 173:         self.status_label.grid(row=0, column=0, sticky="w")
 174:         
 175:         # 设置权重
 176:         self.main_frame.grid_rowconfigure(5, weight=1)
 177:         self.main_frame.grid_columnconfigure(0, weight=1)
 178:     
 179:     def _bind_events(self):
 180:         """绑定事件"""
 181:         self.root.protocol("WM_DELETE_WINDOW", self._exit_application)
 182:     
 183:     def _set_default_values(self):
 184:         """设置默认值"""
 185:         # 设置默认输出目录为当前目录
 186:         #self.output_dir_var.set(os.getcwd())
 187:     
 188:     def _browse_input_file(self):
 189:         """浏览输入文件"""
 190:         filetypes = [
 191:             ("栅格文件", "*.tif *.tiff *.img *.nc *.hdf *.h5"),
 192:             ("TIFF文件", "*.tif *.tiff"),
 193:             ("所有文件", "*.*")
 194:         ]
 195:         
 196:         filename = filedialog.askopenfilename(
 197:             title="选择输入栅格文件",
 198:             filetypes=filetypes
 199:         )
 200:         
 201:         if filename:
 202:             self.input_path_var.set(filename)
 203:             # 自动设置输出目录为输入文件所在目录
 204:             if not self.output_dir_var.get():
 205:                 self.output_dir_var.set(os.path.dirname(filename)+"/output")
 206:     
 207:     def _browse_output_dir(self):
 208:         """浏览输出目录"""
 209:         directory = filedialog.askdirectory(title="选择输出目录")
 210:         if directory:
 211:             self.output_dir_var.set(directory)
 212:     
 213:     def _validate_inputs(self) -> bool:
 214:         """验证输入"""
 215:         if not self.input_path_var.get():
 216:             messagebox.showerror("错误", "请选择输入文件")
 217:             return False
 218:         
 219:         if not os.path.exists(self.input_path_var.get()):
 220:             messagebox.showerror("错误", "输入文件不存在")
 221:             return False
 222:         
 223:         if not self.output_dir_var.get():
 224:             messagebox.showerror("错误", "请选择输出目录")
 225:             return False
 226:         
 227:         if not self.output_name_var.get():
 228:             messagebox.showerror("错误", "请输入输出文件名")
 229:             return False
 230:         
 231:         if not (self.output_raster_var.get() or self.output_vector_var.get()):
 232:             messagebox.showerror("错误", "请至少选择一种输出格式")
 233:             return False
 234:         
 235:         return True
 236:     
 237:     def _create_config(self) -> ProcessingConfig:
 238:         """创建处理配置"""
 239:         output_dir = self.output_dir_var.get()
 240:         output_name = self.output_name_var.get()
 241:         
 242:         config = ProcessingConfig(
 243:             input_file=self.input_path_var.get(),
 244:             grid_level=self.param_panel.get_grid_level(),
 245:             aggregation_method=self.param_panel.get_aggregation_method(),
 246:             window_size=self.param_panel.get_window_size(),
 247:             boundary_handling=self.param_panel.get_boundary_handling(),
 248:             output_dtype=self.param_panel.get_output_dtype(),
 249:             compression=self.param_panel.get_compression(),
 250:             create_overview=self.param_panel.get_create_overview(),
 251:             output_integer_raster=self._should_output_integer_raster(),
 252:             parallel_processing=self.param_panel.get_parallel_processing(),
 253:             max_memory_gb=self.param_panel.get_max_memory(),
 254:             verbose=True
 255:         )
 256:         
 257:         # 设置输出文件路径
 258:         if self.output_raster_var.get():
 259:             config.output_raster = os.path.join(output_dir, f"{output_name}.tif")
 260: 
 261:         if self.output_vector_var.get():
 262:             config.output_vector = os.path.join(output_dir, f"{output_name}.shp")
 263:         
 264:         return config
 265: 
 266:     def _should_output_integer_raster(self) -> bool:
 267:         """判断是否应该输出整型栅格"""
 268:         # 当数据类型选择为整型时，输出整型栅格
 269:         dtype = self.param_panel.get_output_dtype()
 270:         return dtype in ["int16", "int32"]
 271: 
 272:     def _start_processing(self):
 273:         """开始处理"""
 274:         if not self._validate_inputs():
 275:             return
 276:         
 277:         # 禁用处理按钮
 278:         self.process_btn.config(state="disabled")
 279:         self.status_var.set("正在处理...")
 280:         
 281:         # 清空结果显示（隐藏）
 282:         # self.result_text.delete(1.0, tk.END)
 283:         
 284:         # 创建配置
 285:         config = self._create_config()
 286:         
 287:         # 在新线程中执行处理
 288:         thread = threading.Thread(target=self._process_thread, args=(config,))
 289:         thread.daemon = True
 290:         thread.start()
 291:     
 292:     def _process_thread(self, config: ProcessingConfig):
 293:         """处理线程"""
 294:         try:
 295:             # 创建处理器
 296:             processor = BeidouGridProcessor(config)
 297:             self.current_processor = processor  # 保存当前处理器引用
 298: 
 299:             # 创建进度对话框
 300:             self.progress_dialog = ProgressDialog(self.root)
 301:             processor.set_progress_callback(self.progress_dialog.update_progress)
 302: 
 303:             # 设置取消回调
 304:             self.progress_dialog.set_cancel_callback(self._cancel_processing)
 305: 
 306:             # 显示进度对话框
 307:             self.root.after(0, self.progress_dialog.show)
 308: 
 309:             # 执行处理
 310:             result = processor.process()
 311: 
 312:             # 更新界面
 313:             self.root.after(0, self._processing_completed, result)
 314: 
 315:         except InterruptedError:
 316:             # 处理被取消
 317:             self.root.after(0, self._processing_cancelled)
 318:         except Exception as e:
 319:             self.root.after(0, self._processing_error, str(e))
 320:         finally:
 321:             self.current_processor = None
 322:     
 323:     def _processing_completed(self, result: dict):
 324:         """处理完成"""
 325:         # 关闭进度对话框
 326:         if self.progress_dialog:
 327:             self.progress_dialog.close()
 328:         
 329:         # 启用处理按钮
 330:         self.process_btn.config(state="normal")
 331:         
 332:         if result['success']:
 333:             self.status_var.set("处理完成")
 334:             
 335:             # 显示结果
 336:             self._display_result(result)
 337:             
 338:             messagebox.showinfo("成功", "处理完成！")
 339:         else:
 340:             self.status_var.set("处理失败")
 341:             messagebox.showerror("错误", f"处理失败: {result.get('error', '未知错误')}")
 342:     
 343:     def _processing_error(self, error_msg: str):
 344:         """处理错误"""
 345:         # 关闭进度对话框
 346:         if self.progress_dialog:
 347:             self.progress_dialog.close()
 348: 
 349:         # 启用处理按钮
 350:         self.process_btn.config(state="normal")
 351:         self.status_var.set("处理失败")
 352: 
 353:         messagebox.showerror("错误", f"处理失败: {error_msg}")
 354: 
 355:     def _processing_cancelled(self):
 356:         """处理被取消"""
 357:         # 关闭进度对话框
 358:         if self.progress_dialog:
 359:             self.progress_dialog.close()
 360: 
 361:         # 启用处理按钮
 362:         self.process_btn.config(state="normal")
 363: 
 364:         self.status_var.set("处理已取消")
 365:         messagebox.showinfo("取消", "处理已被用户取消")
 366: 
 367:     def _cancel_processing(self):
 368:         """取消处理"""
 369:         if self.current_processor:
 370:             self.current_processor.cancel()
 371:     
 372:     def _display_result(self, result: dict):
 373:         """显示结果（隐藏文本显示，由进度条替代）"""
 374:         # self.result_text.delete(1.0, tk.END)
 375:         #
 376:         # text = "处理结果:\n"
 377:         # text += f"输入数据形状: {result.get('input_shape', 'N/A')}\n"
 378:         # text += f"输出数据形状: {result.get('output_shape', 'N/A')}\n"
 379:         # text += f"网格级别: {result.get('grid_level', 'N/A')}\n"
 380:         # text += f"聚合方法: {result.get('aggregation_method', 'N/A')}\n"
 381:         #
 382:         # if 'output_files' in result:
 383:         #     text += "\n输出文件:\n"
 384:         #     for file_type, file_path in result['output_files'].items():
 385:         #         text += f"  {file_type}: {file_path}\n"
 386:         #
 387:         # if 'statistics' in result:
 388:         #     stats = result['statistics']
 389:         #     text += f"\n统计信息:\n"
 390:         #     text += f"  有效像素数: {stats.get('count', 'N/A')}\n"
 391:         #     text += f"  最小值: {stats.get('min', 'N/A'):.4f}\n"
 392:         #     text += f"  最大值: {stats.get('max', 'N/A'):.4f}\n"
 393:         #     text += f"  平均值: {stats.get('mean', 'N/A'):.4f}\n"
 394:         #     text += f"  标准差: {stats.get('std', 'N/A'):.4f}\n"
 395:         #
 396:         # self.result_text.insert(tk.END, text)
 397: 
 398:         # 只更新状态栏显示处理完成
 399:         self.status_var.set("处理完成")
 400:     
 401:     def _reset_form(self):
 402:         """重置表单"""
 403:         self.input_path_var.set("")
 404:         self.output_dir_var.set(os.getcwd())
 405:         self.output_name_var.set("output")
 406:         self.output_raster_var.set(True)
 407:         self.output_vector_var.set(True)
 408:         self.param_panel.reset_to_defaults()
 409:         # self.result_text.delete(1.0, tk.END)
 410:         self.status_var.set("就绪")
 411:     
 412:     def _exit_application(self):
 413:         """退出应用程序"""
 414:         if messagebox.askokcancel("退出", "确定要退出吗？"):
 415:             self.root.quit()
 416:             self.root.destroy()
 417: 
 418: 
 419:     
 420:     def run(self):
 421:         """运行应用程序"""
 422:         self.root.mainloop()
 423: 


================================================================================
文件 14: gui\parameter_panel.py
================================================================================

   1: """
   2: 参数配置面板
   3: """
   4: 
   5: import tkinter as tk
   6: from tkinter import ttk
   7: import sys
   8: import os
   9: sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
  10: 
  11: from core.aggregation import AggregationMethod
  12: 
  13: 
  14: class ParameterPanel:
  15:     """参数配置面板"""
  16:     
  17:     def __init__(self, parent):
  18:         """
  19:         初始化参数面板
  20:         
  21:         Args:
  22:             parent: 父窗口
  23:         """
  24:         self.parent = parent
  25:         self.frame = ttk.LabelFrame(parent, text="处理参数", padding="10")
  26:         
  27:         # 初始化变量
  28:         self._init_variables()
  29:         
  30:         # 创建界面
  31:         self._create_widgets()
  32:         self._setup_layout()
  33:         
  34:         # 设置默认值
  35:         self._set_defaults()
  36:     
  37:     def _init_variables(self):
  38:         """初始化变量"""
  39:         # 网格参数
  40:         self.grid_level_var = tk.IntVar(value=7)
  41:         
  42:         # 聚合参数
  43:         self.aggregation_method_var = tk.StringVar(value="max")
  44:         self.window_size_var = tk.IntVar(value=5)
  45:         self.boundary_handling_var = tk.StringVar(value="edge")
  46:         
  47:         # 输出参数
  48:         self.output_dtype_var = tk.StringVar(value="float32")
  49:         self.compression_var = tk.StringVar(value="lzw")
  50:         self.create_overview_var = tk.BooleanVar(value=True)
  51:         
  52:         # 处理参数
  53:         self.parallel_processing_var = tk.BooleanVar(value=False)
  54:         self.max_memory_var = tk.DoubleVar(value=4.0)
  55:         self.chunk_size_var = tk.IntVar(value=1024)
  56:     
  57:     def _create_widgets(self):
  58:         """创建界面组件"""
  59:         # 网格参数组
  60:         self.grid_frame = ttk.LabelFrame(self.frame, text="网格参数", padding="5")
  61:         
  62:         # 网格级别
  63:         self.grid_level_label = ttk.Label(self.grid_frame, text="网格级别 (1-10):")
  64:         self.grid_level_scale = ttk.Scale(
  65:             self.grid_frame, 
  66:             from_=1, to=10, 
  67:             orient=tk.HORIZONTAL,
  68:             variable=self.grid_level_var,
  69:             command=self._on_grid_level_change
  70:         )
  71:         self.grid_level_value = ttk.Label(self.grid_frame, text="7")
  72:         
  73:         # 网格级别描述
  74:         self.grid_desc_var = tk.StringVar()
  75:         self.grid_desc_label = ttk.Label(
  76:             self.grid_frame,
  77:             textvariable=self.grid_desc_var,
  78:             font=("Arial", 8),
  79:             foreground="blue",
  80:             wraplength=300
  81:         )
  82:         
  83:         # 聚合参数组
  84:         self.agg_frame = ttk.LabelFrame(self.frame, text="聚合参数", padding="5")
  85:         
  86:         # 聚合方法
  87:         self.agg_method_label = ttk.Label(self.agg_frame, text="聚合方法:")
  88:         self.agg_method_combo = ttk.Combobox(
  89:             self.agg_frame,
  90:             textvariable=self.aggregation_method_var,
  91:             values=self._get_aggregation_methods(),
  92:             state="readonly",
  93:             width=15
  94:         )
  95:         self.agg_method_combo.bind('<<ComboboxSelected>>', self._on_aggregation_method_change)
  96:         
  97:         # 窗口大小
  98:         self.window_size_label = ttk.Label(self.agg_frame, text="窗口大小:")
  99:         self.window_size_spin = ttk.Spinbox(
 100:             self.agg_frame,
 101:             from_=3, to=21, increment=2,
 102:             textvariable=self.window_size_var,
 103:             width=10,
 104:             command=self._on_window_size_change
 105:         )
 106:         self.window_size_spin.bind('<KeyRelease>', self._on_window_size_change)
 107:         
 108:         # 边界处理
 109:         self.boundary_label = ttk.Label(self.agg_frame, text="边界处理:")
 110:         self.boundary_combo = ttk.Combobox(
 111:             self.agg_frame,
 112:             textvariable=self.boundary_handling_var,
 113:             values=["edge", "constant", "reflect", "wrap"],
 114:             state="readonly",
 115:             width=15
 116:         )
 117:         
 118:         # 输出参数组
 119:         self.output_frame = ttk.LabelFrame(self.frame, text="输出参数", padding="5")
 120:         
 121:         # 数据类型
 122:         self.dtype_label = ttk.Label(self.output_frame, text="数据类型:")
 123:         self.dtype_combo = ttk.Combobox(
 124:             self.output_frame,
 125:             textvariable=self.output_dtype_var,
 126:             values=["float32", "float64", "int16", "int32"],
 127:             state="readonly",
 128:             width=15
 129:         )
 130:         self.dtype_combo.bind('<<ComboboxSelected>>', self._on_dtype_change)
 131:         
 132:         # 压缩方式
 133:         self.compression_label = ttk.Label(self.output_frame, text="压缩方式:")
 134:         self.compression_combo = ttk.Combobox(
 135:             self.output_frame,
 136:             textvariable=self.compression_var,
 137:             values=["none", "lzw", "deflate", "packbits"],
 138:             state="readonly",
 139:             width=15
 140:         )
 141:         self.compression_combo.bind('<<ComboboxSelected>>', self._on_compression_change)
 142:         
 143:         # 创建概览图
 144:         self.overview_check = ttk.Checkbutton(
 145:             self.output_frame,
 146:             text="创建概览图",
 147:             variable=self.create_overview_var
 148:         )
 149: 
 150: 
 151:         
 152:         # 高级参数组
 153:         self.advanced_frame = ttk.LabelFrame(self.frame, text="高级参数", padding="5")
 154:         
 155:         # 并行处理
 156:         self.parallel_check = ttk.Checkbutton(
 157:             self.advanced_frame,
 158:             text="启用并行处理",
 159:             variable=self.parallel_processing_var
 160:         )
 161:         
 162:         # 最大内存
 163:         self.memory_label = ttk.Label(self.advanced_frame, text="最大内存 (GB):")
 164:         self.memory_spin = ttk.Spinbox(
 165:             self.advanced_frame,
 166:             from_=1.0, to=32.0, increment=0.5,
 167:             textvariable=self.max_memory_var,
 168:             width=10,
 169:             format="%.1f"
 170:         )
 171:         
 172:         # 块大小
 173:         self.chunk_label = ttk.Label(self.advanced_frame, text="块大小:")
 174:         self.chunk_spin = ttk.Spinbox(
 175:             self.advanced_frame,
 176:             from_=256, to=4096, increment=256,
 177:             textvariable=self.chunk_size_var,
 178:             width=10
 179:         )
 180: 
 181:         # 初始化动态说明
 182:         self._update_dynamic_help()
 183:     
 184:     def _setup_layout(self):
 185:         """设置布局"""
 186:         # 网格参数组
 187:         self.grid_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
 188:         self.grid_frame.grid_columnconfigure(1, weight=1)
 189:         
 190:         self.grid_level_label.grid(row=0, column=0, sticky="w", padx=(0, 10))
 191:         self.grid_level_scale.grid(row=0, column=1, sticky="ew", padx=(0, 10))
 192:         self.grid_level_value.grid(row=0, column=2)
 193:         self.grid_desc_label.grid(row=1, column=0, columnspan=3, sticky="w", pady=(5, 0))
 194:         
 195:         # 聚合参数组
 196:         self.agg_frame.grid(row=1, column=0, sticky="ew", padx=(0, 10), pady=(0, 10))
 197:         self.agg_frame.grid_columnconfigure(1, weight=1)
 198: 
 199:         self.agg_method_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
 200:         self.agg_method_combo.grid(row=0, column=1, sticky="w", pady=(0, 5), padx=(5, 0))
 201: 
 202:         self.window_size_label.grid(row=1, column=0, sticky="w", pady=(0, 5))
 203:         self.window_size_spin.grid(row=1, column=1, sticky="w", pady=(0, 5), padx=(5, 0))
 204: 
 205:         self.boundary_label.grid(row=2, column=0, sticky="w")
 206:         self.boundary_combo.grid(row=2, column=1, sticky="w", padx=(5, 0))
 207:         
 208:         # 输出参数组
 209:         self.output_frame.grid(row=1, column=1, sticky="ew", pady=(0, 10))
 210:         self.output_frame.grid_columnconfigure(1, weight=1)
 211: 
 212:         self.dtype_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
 213:         self.dtype_combo.grid(row=0, column=1, sticky="w", pady=(0, 5), padx=(5, 0))
 214: 
 215:         self.compression_label.grid(row=1, column=0, sticky="w", pady=(0, 5))
 216:         self.compression_combo.grid(row=1, column=1, sticky="w", pady=(0, 5), padx=(5, 0))
 217: 
 218:         self.overview_check.grid(row=2, column=0, columnspan=2, sticky="w")
 219:         
 220:         # 高级参数组
 221:         self.advanced_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
 222:         self.advanced_frame.grid_columnconfigure(1, weight=1)
 223: 
 224:         self.parallel_check.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 5))
 225: 
 226:         self.memory_label.grid(row=1, column=0, sticky="w", padx=(0, 10))
 227:         self.memory_spin.grid(row=1, column=1, sticky="w")
 228: 
 229:         self.chunk_label.grid(row=1, column=2, sticky="w", padx=(20, 10))
 230:         self.chunk_spin.grid(row=1, column=3, sticky="w")
 231: 
 232:         # 设置权重
 233:         self.frame.grid_columnconfigure(0, weight=1)
 234:         self.frame.grid_columnconfigure(1, weight=1)
 235:     
 236:     def _set_defaults(self):
 237:         """设置默认值"""
 238:         self._update_grid_description()
 239:         self._update_dynamic_help()
 240:     
 241:     def _get_aggregation_methods(self):
 242:         """获取聚合方法列表"""
 243:         methods = []
 244:         for method in AggregationMethod:
 245:             methods.append(method.value)
 246:         return methods
 247:     
 248:     def _on_grid_level_change(self, value):
 249:         """网格级别改变事件"""
 250:         level = int(float(value))
 251:         self.grid_level_value.config(text=str(level))
 252:         self._update_grid_description()
 253: 
 254:     def _on_aggregation_method_change(self, event=None):
 255:         """聚合方法改变时的回调"""
 256:         pass
 257: 
 258:     def _on_window_size_change(self, event=None):
 259:         """窗口大小改变时的回调"""
 260:         pass
 261: 
 262:     def _on_dtype_change(self, event=None):
 263:         """数据类型改变时的回调"""
 264:         pass
 265: 
 266:     def _on_compression_change(self, event=None):
 267:         """压缩方式改变时的回调"""
 268:         pass
 269:     
 270:     def _update_grid_description(self):
 271:         """更新网格级别描述"""
 272:         from core.grid_calculator import BeidouGridCalculator
 273:         
 274:         level = self.grid_level_var.get()
 275:         calculator = BeidouGridCalculator()
 276:         
 277:         if level in calculator.GRID_LEVELS:
 278:             desc = calculator.GRID_LEVELS[level].description
 279:             self.grid_desc_var.set(desc)
 280:         else:
 281:             self.grid_desc_var.set("")
 282:     
 283:     # Getter方法
 284:     def get_grid_level(self) -> int:
 285:         """获取网格级别"""
 286:         return self.grid_level_var.get()
 287:     
 288:     def get_aggregation_method(self) -> str:
 289:         """获取聚合方法"""
 290:         return self.aggregation_method_var.get()
 291:     
 292:     def get_window_size(self) -> int:
 293:         """获取窗口大小"""
 294:         return self.window_size_var.get()
 295:     
 296:     def get_boundary_handling(self) -> str:
 297:         """获取边界处理方式"""
 298:         return self.boundary_handling_var.get()
 299:     
 300:     def get_output_dtype(self) -> str:
 301:         """获取输出数据类型"""
 302:         return self.output_dtype_var.get()
 303:     
 304:     def get_compression(self) -> str:
 305:         """获取压缩方式"""
 306:         return self.compression_var.get()
 307:     
 308:     def get_create_overview(self) -> bool:
 309:         """获取是否创建概览图"""
 310:         return self.create_overview_var.get()
 311:     
 312:     def get_parallel_processing(self) -> bool:
 313:         """获取是否启用并行处理"""
 314:         return self.parallel_processing_var.get()
 315:     
 316:     def get_max_memory(self) -> float:
 317:         """获取最大内存"""
 318:         return self.max_memory_var.get()
 319:     
 320:     def get_chunk_size(self) -> int:
 321:         """获取块大小"""
 322:         return self.chunk_size_var.get()
 323:     
 324:     def reset_to_defaults(self):
 325:         """重置为默认值"""
 326:         self.grid_level_var.set(7)
 327:         self.aggregation_method_var.set("max")
 328:         self.window_size_var.set(5)
 329:         self.boundary_handling_var.set("edge")
 330:         self.output_dtype_var.set("float32")
 331:         self.compression_var.set("lzw")
 332:         self.create_overview_var.set(True)
 333:         self.output_integer_raster_var.set(True)
 334:         self.parallel_processing_var.set(False)
 335:         self.max_memory_var.set(4.0)
 336:         self.chunk_size_var.set(1024)
 337:         self._update_grid_description()
 338: 
 339:     def _update_dynamic_help(self):
 340:         """更新动态说明"""
 341:         pass
 342: 
 343: 
 344: 


================================================================================
文件 15: gui\progress_dialog.py
================================================================================

   1: """
   2: 进度对话框
   3: """
   4: 
   5: import tkinter as tk
   6: from tkinter import ttk
   7: import threading
   8: import time
   9: import logging
  10: from .window_utils import center_window
  11: 
  12: 
  13: class ProgressDialog:
  14:     """进度对话框"""
  15:     
  16:     def __init__(self, parent):
  17:         """
  18:         初始化进度对话框
  19:         
  20:         Args:
  21:             parent: 父窗口
  22:         """
  23:         self.parent = parent
  24:         self.dialog = None
  25:         self.progress_var = tk.IntVar()
  26:         self.message_var = tk.StringVar()
  27:         self.is_cancelled = False
  28:         self.cancel_callback = None
  29: 
  30:         # 时间跟踪
  31:         self.start_time = time.time()
  32:         self.stage_start_time = time.time()
  33:         self.stage_progress_history = []  # 存储每个阶段的进度历史
  34:         self.current_stage = ""
  35:         self.last_percent = 0
  36:         self.last_eta = 0  # 上次预估的剩余时间
  37:         
  38:         # 创建对话框
  39:         self._create_dialog()
  40:     
  41:     def _create_dialog(self):
  42:         """创建对话框"""
  43:         self.dialog = tk.Toplevel(self.parent)
  44:         self.dialog.title("处理进度")
  45: 
  46:         # 居中显示对话框
  47:         center_window(self.dialog, 700, 400)
  48:         self.dialog.minsize(600, 350)    # 设置最小尺寸
  49:         self.dialog.resizable(True, True)  # 允许调整大小
  50: 
  51:         # 设置为模态对话框
  52:         self.dialog.transient(self.parent)
  53:         self.dialog.grab_set()
  54:         
  55:         # 创建界面组件
  56:         self._create_widgets()
  57:         self._setup_layout()
  58:         
  59:         # 绑定关闭事件
  60:         self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
  61:         
  62:         # 初始隐藏
  63:         self.dialog.withdraw()
  64:     
  65:     def _center_dialog(self):
  66:         """居中显示对话框"""
  67:         self.dialog.update_idletasks()
  68:         
  69:         # 获取父窗口位置和大小
  70:         parent_x = self.parent.winfo_rootx()
  71:         parent_y = self.parent.winfo_rooty()
  72:         parent_width = self.parent.winfo_width()
  73:         parent_height = self.parent.winfo_height()
  74:         
  75:         # 获取对话框大小
  76:         dialog_width = self.dialog.winfo_reqwidth()
  77:         dialog_height = self.dialog.winfo_reqheight()
  78:         
  79:         # 计算居中位置
  80:         x = parent_x + (parent_width - dialog_width) // 2
  81:         y = parent_y + (parent_height - dialog_height) // 2
  82:         
  83:         self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
  84:     
  85:     def _create_widgets(self):
  86:         """创建界面组件"""
  87:         # 主框架
  88:         self.main_frame = ttk.Frame(self.dialog, padding="30")
  89: 
  90:         # 标题
  91:         self.title_label = ttk.Label(
  92:             self.main_frame,
  93:             text="正在处理，请稍候...",
  94:             font=("Arial", 16, "bold")
  95:         )
  96: 
  97:         # 进度条
  98:         self.progress_bar = ttk.Progressbar(
  99:             self.main_frame,
 100:             variable=self.progress_var,
 101:             maximum=100,
 102:             length=600,  # 进一步增加进度条长度
 103:             mode='determinate'
 104:         )
 105: 
 106:         # 进度百分比
 107:         self.percent_label = ttk.Label(
 108:             self.main_frame,
 109:             text="0%",
 110:             font=("Arial", 14, "bold")
 111:         )
 112: 
 113:         # 状态消息
 114:         self.message_label = ttk.Label(
 115:             self.main_frame,
 116:             textvariable=self.message_var,
 117:             font=("Arial", 12),
 118:             foreground="blue",
 119:             wraplength=600  # 增加自动换行宽度
 120:         )
 121:         
 122:         # 取消按钮
 123:         self.cancel_button = ttk.Button(
 124:             self.main_frame,
 125:             text="取消处理",
 126:             command=self._on_cancel,
 127:             width=15
 128:         )
 129:         
 130:         # 时间显示
 131:         self.time_var = tk.StringVar()
 132:         self.time_label = ttk.Label(
 133:             self.main_frame,
 134:             textvariable=self.time_var,
 135:             font=("Arial", 11),
 136:             foreground="gray"
 137:         )
 138: 
 139:         # 预计剩余时间显示
 140:         self.eta_var = tk.StringVar()
 141:         self.eta_label = ttk.Label(
 142:             self.main_frame,
 143:             textvariable=self.eta_var,
 144:             font=("Arial", 11),
 145:             foreground="orange"
 146:         )
 147: 
 148:         # 详细信息显示
 149:         self.detail_var = tk.StringVar()
 150:         self.detail_label = ttk.Label(
 151:             self.main_frame,
 152:             textvariable=self.detail_var,
 153:             font=("Arial", 11),
 154:             foreground="darkgreen",
 155:             wraplength=600  # 增加自动换行宽度
 156:         )
 157:         
 158:         # 初始化时间
 159:         self.start_time = None
 160:         self._update_time()
 161:     
 162:     def _setup_layout(self):
 163:         """设置布局"""
 164:         self.main_frame.pack(fill=tk.BOTH, expand=True)
 165: 
 166:         self.title_label.pack(pady=(0, 25))
 167:         self.progress_bar.pack(pady=(0, 15))
 168: 
 169:         # 进度百分比和时间在同一行
 170:         percent_time_frame = ttk.Frame(self.main_frame)
 171:         percent_time_frame.pack(fill=tk.X, pady=(0, 10))
 172: 
 173:         self.percent_label.pack(in_=percent_time_frame, side=tk.LEFT)
 174:         self.time_label.pack(in_=percent_time_frame, side=tk.RIGHT)
 175: 
 176:         # 预计剩余时间
 177:         self.eta_label.pack(pady=(0, 15))
 178: 
 179:         # 状态消息
 180:         self.message_label.pack(pady=(0, 15), fill=tk.X)
 181: 
 182:         # 详细信息
 183:         self.detail_label.pack(pady=(0, 20), fill=tk.X)
 184: 
 185:         # 取消按钮
 186:         self.cancel_button.pack(pady=(10, 0))
 187:     
 188:     def show(self):
 189:         """显示对话框"""
 190:         self.start_time = time.time()
 191:         self.dialog.deiconify()
 192:         self.dialog.lift()
 193:         self.dialog.focus_set()
 194:         
 195:         # 开始更新时间
 196:         self._schedule_time_update()
 197:     
 198:     def close(self):
 199:         """关闭对话框"""
 200:         if self.dialog:
 201:             self.dialog.grab_release()
 202:             self.dialog.destroy()
 203:             self.dialog = None
 204:     
 205:     def update_progress(self, percent: int, message: str = "", detail: str = "", extra_info: dict = None):
 206:         """
 207:         更新进度
 208: 
 209:         Args:
 210:             percent: 进度百分比 (0-100)
 211:             message: 状态消息
 212:             detail: 详细信息
 213:             extra_info: 额外信息（用于更精确的时间估算）
 214:         """
 215:         if self.dialog and self.dialog.winfo_exists():
 216:             self.progress_var.set(percent)
 217:             self.percent_label.config(text=f"{percent}%")
 218: 
 219:             if message:
 220:                 self.message_var.set(message)
 221:                 # 检查是否是新阶段
 222:                 if message != self.current_stage:
 223:                     self.current_stage = message
 224:                     self.stage_start_time = time.time()
 225:                     self.stage_progress_history = []
 226: 
 227:             if detail:
 228:                 self.detail_var.set(detail)
 229: 
 230:             # 更新时间估算
 231:             self._update_time_estimation(percent, extra_info)
 232: 
 233:             # 如果进度达到100%，禁用取消按钮
 234:             if percent >= 100:
 235:                 self.cancel_button.config(state="disabled", text="处理完成")
 236:                 self.eta_var.set("处理完成！")
 237: 
 238:             self.last_percent = percent
 239:             # 更新界面
 240:             self.dialog.update_idletasks()
 241:     
 242:     def set_cancel_callback(self, callback):
 243:         """
 244:         设置取消回调函数
 245:         
 246:         Args:
 247:             callback: 取消时调用的函数
 248:         """
 249:         self.cancel_callback = callback
 250:     
 251:     def _on_cancel(self):
 252:         """取消按钮点击事件"""
 253:         self.is_cancelled = True
 254:         self.cancel_button.config(state="disabled", text="正在取消...")
 255: 
 256:         if self.cancel_callback:
 257:             # 立即执行取消操作
 258:             try:
 259:                 self.cancel_callback()
 260:                 # 取消成功后立即关闭对话框
 261:                 self.dialog.after(100, self.close)
 262:             except Exception as e:
 263:                 logging.error(f"取消操作失败: {e}")
 264:                 # 即使取消失败也关闭对话框
 265:                 self.dialog.after(100, self.close)
 266:     
 267:     def _on_close(self):
 268:         """对话框关闭事件"""
 269:         # 阻止直接关闭，只能通过取消按钮
 270:         pass
 271:     
 272:     def _update_time(self):
 273:         """更新时间显示"""
 274:         if self.start_time and self.dialog and self.dialog.winfo_exists():
 275:             elapsed = time.time() - self.start_time
 276:             
 277:             if elapsed < 60:
 278:                 time_str = f"已用时: {elapsed:.0f}秒"
 279:             elif elapsed < 3600:
 280:                 minutes = int(elapsed // 60)
 281:                 seconds = int(elapsed % 60)
 282:                 time_str = f"已用时: {minutes}分{seconds}秒"
 283:             else:
 284:                 hours = int(elapsed // 3600)
 285:                 minutes = int((elapsed % 3600) // 60)
 286:                 time_str = f"已用时: {hours}小时{minutes}分"
 287:             
 288:             self.time_var.set(time_str)
 289: 
 290:     def _update_time_estimation(self, percent: int, extra_info: dict = None):
 291:         """更新时间估算"""
 292:         if percent <= 0 or not self.start_time:
 293:             return
 294: 
 295:         current_time = time.time()
 296: 
 297:         # 记录当前阶段的进度历史
 298:         if self.current_stage:
 299:             progress_data = {
 300:                 'time': current_time,
 301:                 'percent': percent
 302:             }
 303: 
 304:             # 如果有额外信息（如像素处理信息），添加到历史中
 305:             if extra_info:
 306:                 progress_data.update(extra_info)
 307: 
 308:             self.stage_progress_history.append(progress_data)
 309: 
 310:             # 只保留最近的8个数据点（减少数据点以提高稳定性）
 311:             if len(self.stage_progress_history) > 8:
 312:                 self.stage_progress_history.pop(0)
 313: 
 314:         # 计算预计剩余时间
 315:         eta_text = self._calculate_eta(percent, current_time, extra_info)
 316:         if eta_text:
 317:             self.eta_var.set(eta_text)
 318: 
 319:     def _calculate_eta(self, percent: int, current_time: float, extra_info: dict = None) -> str:
 320:         """计算预计剩余时间"""
 321:         if percent <= 10:  # 进度太少时不显示预估
 322:             return ""
 323: 
 324:         # 如果是数据聚合阶段且有像素处理信息，使用更精确的计算
 325:         if (extra_info and 'pixels_processed' in extra_info and
 326:             'total_pixels' in extra_info and self.current_stage == "执行数据聚合"):
 327:             return self._calculate_pixel_based_eta(extra_info, current_time)
 328: 
 329:         # 使用最近的进度数据计算，避免早期不稳定的数据影响
 330:         if len(self.stage_progress_history) >= 3:  # 需要至少3个数据点
 331:             # 使用最近3个数据点计算平均速度
 332:             recent_data = self.stage_progress_history[-3:]
 333:             time_span = recent_data[-1]['time'] - recent_data[0]['time']
 334:             progress_span = recent_data[-1]['percent'] - recent_data[0]['percent']
 335: 
 336:             if time_span > 0 and progress_span > 0:
 337:                 # 计算平均速度（每秒进度）
 338:                 avg_speed = progress_span / time_span
 339:                 remaining_progress = 100 - percent
 340:                 remaining_time = remaining_progress / avg_speed
 341: 
 342:                 # 平滑处理，避免剧烈波动
 343:                 if self.last_eta > 0:
 344:                     # 使用加权平均，新预估占70%，旧预估占30%
 345:                     remaining_time = remaining_time * 0.7 + self.last_eta * 0.3
 346: 
 347:                 self.last_eta = remaining_time
 348: 
 349:                 if remaining_time > 0 and remaining_time < 7200:  # 限制在2小时内
 350:                     return self._format_time_remaining(remaining_time)
 351: 
 352:         return ""
 353: 
 354:     def _calculate_pixel_based_eta(self, extra_info: dict, current_time: float) -> str:
 355:         """基于像素处理进度计算预计剩余时间"""
 356:         pixels_processed = extra_info['pixels_processed']
 357:         total_pixels = extra_info['total_pixels']
 358: 
 359:         if pixels_processed <= 0:
 360:             return ""
 361: 
 362:         # 计算聚合阶段开始以来的时间
 363:         aggregation_elapsed = current_time - self.stage_start_time
 364: 
 365:         if aggregation_elapsed > 0:
 366:             # 计算像素处理速度（每秒处理的像素数）
 367:             pixel_speed = pixels_processed / aggregation_elapsed
 368:             remaining_pixels = total_pixels - pixels_processed
 369: 
 370:             if pixel_speed > 0:
 371:                 remaining_time = remaining_pixels / pixel_speed
 372: 
 373:                 # 平滑处理
 374:                 if self.last_eta > 0:
 375:                     remaining_time = remaining_time * 0.8 + self.last_eta * 0.2
 376: 
 377:                 self.last_eta = remaining_time
 378: 
 379:                 if remaining_time > 0 and remaining_time < 3600:  # 限制在1小时内
 380:                     return self._format_time_remaining(remaining_time)
 381: 
 382:         return ""
 383: 
 384:     def _calculate_stage_eta(self) -> float:
 385:         """基于当前阶段的进度历史计算预估时间"""
 386:         if len(self.stage_progress_history) < 3:
 387:             return 0
 388: 
 389:         # 计算最近几个数据点的平均速度
 390:         recent_data = self.stage_progress_history[-3:]
 391:         time_diff = recent_data[-1]['time'] - recent_data[0]['time']
 392:         progress_diff = recent_data[-1]['percent'] - recent_data[0]['percent']
 393: 
 394:         if time_diff > 0 and progress_diff > 0:
 395:             speed = progress_diff / time_diff  # 每秒进度
 396:             remaining_progress = 100 - recent_data[-1]['percent']
 397:             return remaining_progress / speed
 398: 
 399:         return 0
 400: 
 401:     def _format_time_remaining(self, seconds: float) -> str:
 402:         """格式化剩余时间显示"""
 403:         seconds = int(seconds)
 404: 
 405:         if seconds < 60:
 406:             return f"预计剩余: {seconds}秒"
 407:         elif seconds < 3600:
 408:             minutes = seconds // 60
 409:             secs = seconds % 60
 410:             return f"预计剩余: {minutes}分{secs}秒"
 411:         else:
 412:             hours = seconds // 3600
 413:             minutes = (seconds % 3600) // 60
 414:             return f"预计剩余: {hours}小时{minutes}分钟"
 415: 
 416:     def _schedule_time_update(self):
 417:         """定时更新时间"""
 418:         if self.dialog and self.dialog.winfo_exists():
 419:             self._update_time()
 420:             # 每秒更新一次
 421:             self.dialog.after(1000, self._schedule_time_update)
 422: 
 423: 
 424: class IndeterminateProgressDialog(ProgressDialog):
 425:     """不确定进度的对话框"""
 426:     
 427:     def _create_widgets(self):
 428:         """创建界面组件"""
 429:         super()._create_widgets()
 430:         
 431:         # 修改进度条为不确定模式
 432:         self.progress_bar.config(mode='indeterminate')
 433:         
 434:         # 隐藏百分比标签
 435:         self.percent_label.pack_forget()
 436:     
 437:     def show(self):
 438:         """显示对话框"""
 439:         super().show()
 440:         # 开始不确定进度动画
 441:         self.progress_bar.start(10)
 442:     
 443:     def close(self):
 444:         """关闭对话框"""
 445:         if self.progress_bar:
 446:             self.progress_bar.stop()
 447:         super().close()
 448:     
 449:     def update_progress(self, percent: int = 0, message: str = "", detail: str = ""):
 450:         """
 451:         更新进度（忽略百分比）
 452: 
 453:         Args:
 454:             percent: 忽略
 455:             message: 状态消息
 456:             detail: 详细信息
 457:         """
 458:         if self.dialog and self.dialog.winfo_exists():
 459:             if message:
 460:                 self.message_var.set(message)
 461: 
 462:             if detail:
 463:                 self.detail_var.set(detail)
 464:             
 465:             # 更新界面
 466:             self.dialog.update_idletasks()
 467: 


================================================================================
文件 16: gui\window_utils.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 窗口工具函数
   4: """
   5: 
   6: import tkinter as tk
   7: from typing import Union
   8: 
   9: 
  10: def center_window(window: Union[tk.Tk, tk.Toplevel], width: int, height: int):
  11:     """
  12:     将窗口居中显示在屏幕上
  13:     
  14:     Args:
  15:         window: 要居中的窗口对象
  16:         width: 窗口宽度
  17:         height: 窗口高度
  18:     """
  19:     # 获取屏幕尺寸
  20:     screen_width = window.winfo_screenwidth()
  21:     screen_height = window.winfo_screenheight()
  22:     
  23:     # 计算居中位置
  24:     center_x = int(screen_width / 2 - width / 2)
  25:     center_y = int(screen_height / 2 - height / 2)
  26:     
  27:     # 确保窗口不会超出屏幕边界
  28:     center_x = max(0, center_x)
  29:     center_y = max(0, center_y)
  30:     
  31:     # 设置窗口位置和大小
  32:     window.geometry(f"{width}x{height}+{center_x}+{center_y}")
  33: 
  34: 
  35: def center_window_on_parent(child: tk.Toplevel, parent: Union[tk.Tk, tk.Toplevel], width: int, height: int):
  36:     """
  37:     将子窗口居中显示在父窗口上
  38:     
  39:     Args:
  40:         child: 子窗口对象
  41:         parent: 父窗口对象
  42:         width: 子窗口宽度
  43:         height: 子窗口高度
  44:     """
  45:     # 更新父窗口以获取准确的位置和大小
  46:     parent.update_idletasks()
  47:     
  48:     # 获取父窗口的位置和大小
  49:     parent_x = parent.winfo_x()
  50:     parent_y = parent.winfo_y()
  51:     parent_width = parent.winfo_width()
  52:     parent_height = parent.winfo_height()
  53:     
  54:     # 计算子窗口在父窗口中的居中位置
  55:     center_x = parent_x + int(parent_width / 2 - width / 2)
  56:     center_y = parent_y + int(parent_height / 2 - height / 2)
  57:     
  58:     # 获取屏幕尺寸以确保窗口不会超出屏幕
  59:     screen_width = child.winfo_screenwidth()
  60:     screen_height = child.winfo_screenheight()
  61:     
  62:     # 确保窗口不会超出屏幕边界
  63:     center_x = max(0, min(center_x, screen_width - width))
  64:     center_y = max(0, min(center_y, screen_height - height))
  65:     
  66:     # 设置子窗口位置和大小
  67:     child.geometry(f"{width}x{height}+{center_x}+{center_y}")
  68: 
  69: 
  70: def get_screen_size(window: Union[tk.Tk, tk.Toplevel]) -> tuple:
  71:     """
  72:     获取屏幕尺寸
  73:     
  74:     Args:
  75:         window: 窗口对象
  76:         
  77:     Returns:
  78:         (width, height): 屏幕宽度和高度
  79:     """
  80:     return window.winfo_screenwidth(), window.winfo_screenheight()
  81: 
  82: 
  83: def get_optimal_window_size(screen_width: int, screen_height: int, 
  84:                           min_width: int = 800, min_height: int = 600,
  85:                           max_ratio: float = 0.8) -> tuple:
  86:     """
  87:     根据屏幕尺寸计算最佳窗口大小
  88:     
  89:     Args:
  90:         screen_width: 屏幕宽度
  91:         screen_height: 屏幕高度
  92:         min_width: 最小窗口宽度
  93:         min_height: 最小窗口高度
  94:         max_ratio: 窗口占屏幕的最大比例
  95:         
  96:     Returns:
  97:         (width, height): 最佳窗口宽度和高度
  98:     """
  99:     # 计算基于屏幕比例的窗口大小
 100:     optimal_width = int(screen_width * max_ratio)
 101:     optimal_height = int(screen_height * max_ratio)
 102:     
 103:     # 确保不小于最小尺寸
 104:     width = max(min_width, optimal_width)
 105:     height = max(min_height, optimal_height)
 106:     
 107:     return width, height
 108: 
 109: 
 110: def set_window_icon(window: Union[tk.Tk, tk.Toplevel], icon_path: str):
 111:     """
 112:     设置窗口图标
 113:     
 114:     Args:
 115:         window: 窗口对象
 116:         icon_path: 图标文件路径
 117:     """
 118:     try:
 119:         window.iconbitmap(icon_path)
 120:     except Exception:
 121:         # 如果设置图标失败，忽略错误
 122:         pass
 123: 
 124: 
 125: def make_window_modal(dialog: tk.Toplevel, parent: Union[tk.Tk, tk.Toplevel]):
 126:     """
 127:     将对话框设置为模态
 128:     
 129:     Args:
 130:         dialog: 对话框窗口
 131:         parent: 父窗口
 132:     """
 133:     dialog.transient(parent)
 134:     dialog.grab_set()
 135:     dialog.focus_set()
 136: 
 137: 
 138: def configure_window_properties(window: Union[tk.Tk, tk.Toplevel], 
 139:                               title: str,
 140:                               width: int, 
 141:                               height: int,
 142:                               min_width: int = None,
 143:                               min_height: int = None,
 144:                               resizable: bool = True,
 145:                               center: bool = True,
 146:                               icon_path: str = None):
 147:     """
 148:     配置窗口的基本属性
 149:     
 150:     Args:
 151:         window: 窗口对象
 152:         title: 窗口标题
 153:         width: 窗口宽度
 154:         height: 窗口高度
 155:         min_width: 最小宽度
 156:         min_height: 最小高度
 157:         resizable: 是否可调整大小
 158:         center: 是否居中显示
 159:         icon_path: 图标文件路径
 160:     """
 161:     window.title(title)
 162:     
 163:     if center:
 164:         center_window(window, width, height)
 165:     else:
 166:         window.geometry(f"{width}x{height}")
 167:     
 168:     if min_width and min_height:
 169:         window.minsize(min_width, min_height)
 170:     
 171:     window.resizable(resizable, resizable)
 172:     
 173:     if icon_path:
 174:         set_window_icon(window, icon_path)
 175: 


================================================================================
文件 17: main.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 北斗网格转换工具 v2.0
   4: 主程序入口
   5: 
   6: 使用方法:
   7: 1. GUI模式: python main.py
   8: 2. 命令行模式: python main.py --cli [参数]
   9: 3. 帮助信息: python main.py --help
  10: """
  11: 
  12: import sys
  13: import os
  14: import argparse
  15: import logging
  16: from typing import Optional
  17: 
  18: # 添加项目根目录到路径
  19: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  20: 
  21: from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
  22: from core.aggregation import AggregationMethod
  23: 
  24: 
  25: def setup_logging(verbose: bool = False):
  26:     """设置日志"""
  27:     level = logging.INFO if verbose else logging.WARNING
  28:     logging.basicConfig(
  29:         level=level,
  30:         format='%(asctime)s - %(levelname)s - %(message)s',
  31:         datefmt='%Y-%m-%d %H:%M:%S'
  32:     )
  33: 
  34: 
  35: def run_gui():
  36:     """运行GUI界面"""
  37:     try:
  38:         from gui.main_window import MainWindow
  39:         
  40:         print("启动北斗网格转换工具 GUI...")
  41:         app = MainWindow()
  42:         app.run()
  43:         
  44:     except ImportError as e:
  45:         print(f"GUI依赖缺失: {e}")
  46:         print("请安装tkinter: pip install tkinter")
  47:         sys.exit(1)
  48:     except Exception as e:
  49:         print(f"GUI启动失败: {e}")
  50:         sys.exit(1)
  51: 
  52: 
  53: def run_cli(args):
  54:     """运行命令行模式"""
  55:     try:
  56:         # 创建配置
  57:         config = ProcessingConfig(
  58:             input_file=args.input,
  59:             output_raster=args.output_raster,
  60:             output_vector=args.output_vector,
  61:             grid_level=args.grid_level,
  62:             aggregation_method=args.aggregation_method,
  63:             window_size=args.window_size,
  64:             boundary_handling=args.boundary_handling,
  65:             output_dtype=args.output_dtype,
  66:             compression=args.compression,
  67:             create_overview=args.create_overview,
  68:             parallel_processing=args.parallel,
  69:             max_memory_gb=args.max_memory,
  70:             verbose=args.verbose
  71:         )
  72:         
  73:         # 验证输入
  74:         if not os.path.exists(config.input_file):
  75:             print(f"错误: 输入文件不存在: {config.input_file}")
  76:             sys.exit(1)
  77:         
  78:         if not (config.output_raster or config.output_vector):
  79:             print("错误: 必须指定至少一种输出格式")
  80:             sys.exit(1)
  81:         
  82:         # 创建处理器
  83:         processor = BeidouGridProcessor(config)
  84:         
  85:         # 设置进度回调
  86:         def progress_callback(percent, message):
  87:             print(f"进度 {percent}%: {message}")
  88:         
  89:         processor.set_progress_callback(progress_callback)
  90:         
  91:         # 执行处理
  92:         print("开始处理...")
  93:         result = processor.process()
  94:         
  95:         if result['success']:
  96:             print("处理完成!")
  97:             print(f"输入形状: {result.get('input_shape')}")
  98:             print(f"输出形状: {result.get('output_shape')}")
  99:             print(f"网格级别: {result.get('grid_level')}")
 100:             print(f"聚合方法: {result.get('aggregation_method')}")
 101:             
 102:             if 'output_files' in result:
 103:                 print("输出文件:")
 104:                 for file_type, file_path in result['output_files'].items():
 105:                     print(f"  {file_type}: {file_path}")
 106:             
 107:             if 'statistics' in result:
 108:                 stats = result['statistics']
 109:                 print("统计信息:")
 110:                 print(f"  有效像素数: {stats.get('count')}")
 111:                 print(f"  最小值: {stats.get('min', 0):.4f}")
 112:                 print(f"  最大值: {stats.get('max', 0):.4f}")
 113:                 print(f"  平均值: {stats.get('mean', 0):.4f}")
 114:                 print(f"  标准差: {stats.get('std', 0):.4f}")
 115:         else:
 116:             print(f"处理失败: {result.get('error')}")
 117:             sys.exit(1)
 118:             
 119:     except Exception as e:
 120:         print(f"处理失败: {e}")
 121:         if args.verbose:
 122:             import traceback
 123:             traceback.print_exc()
 124:         sys.exit(1)
 125: 
 126: 
 127: def create_parser():
 128:     """创建命令行参数解析器"""
 129:     parser = argparse.ArgumentParser(
 130:         description="北斗网格转换工具 v2.0",
 131:         formatter_class=argparse.RawDescriptionHelpFormatter,
 132:         epilog="""
 133: 示例:
 134:   # GUI模式
 135:   python main.py
 136:   
 137:   # 命令行模式 - 基本用法
 138:   python main.py --cli -i input.tif -or output.tif -ov output.shp
 139:   
 140:   # 命令行模式 - 完整参数
 141:   python main.py --cli -i input.tif -or output.tif -ov output.shp \\
 142:     --grid-level 8 --aggregation-method mean --window-size 7 \\
 143:     --output-dtype float32 --compression lzw --parallel --verbose
 144:         """
 145:     )
 146:     
 147:     # 模式选择
 148:     parser.add_argument(
 149:         '--cli', action='store_true',
 150:         help='使用命令行模式（默认为GUI模式）'
 151:     )
 152:     
 153:     # 输入输出参数
 154:     parser.add_argument(
 155:         '-i', '--input', type=str,
 156:         help='输入栅格文件路径'
 157:     )
 158:     parser.add_argument(
 159:         '-or', '--output-raster', type=str,
 160:         help='输出栅格文件路径'
 161:     )
 162:     parser.add_argument(
 163:         '-ov', '--output-vector', type=str,
 164:         help='输出矢量文件路径'
 165:     )
 166:     
 167:     # 网格参数
 168:     parser.add_argument(
 169:         '--grid-level', type=int, default=7, choices=range(1, 11),
 170:         help='网格级别 (1-10，默认: 7)'
 171:     )
 172:     
 173:     # 聚合参数
 174:     aggregation_methods = [method.value for method in AggregationMethod]
 175:     parser.add_argument(
 176:         '--aggregation-method', type=str, default='max',
 177:         choices=aggregation_methods,
 178:         help=f'聚合方法 (默认: max)'
 179:     )
 180:     parser.add_argument(
 181:         '--window-size', type=int, default=5,
 182:         help='窗口大小 (默认: 5)'
 183:     )
 184:     parser.add_argument(
 185:         '--boundary-handling', type=str, default='edge',
 186:         choices=['edge', 'constant', 'reflect', 'wrap'],
 187:         help='边界处理方式 (默认: edge)'
 188:     )
 189:     
 190:     # 输出参数
 191:     parser.add_argument(
 192:         '--output-dtype', type=str, default='float32',
 193:         choices=['float32', 'float64', 'int16', 'int32'],
 194:         help='输出数据类型 (默认: float32)'
 195:     )
 196:     parser.add_argument(
 197:         '--compression', type=str, default='lzw',
 198:         choices=['none', 'lzw', 'deflate', 'packbits'],
 199:         help='压缩方式 (默认: lzw)'
 200:     )
 201:     parser.add_argument(
 202:         '--create-overview', action='store_true', default=True,
 203:         help='创建概览图 (默认: True)'
 204:     )
 205:     parser.add_argument(
 206:         '--no-overview', dest='create_overview', action='store_false',
 207:         help='不创建概览图'
 208:     )
 209:     
 210:     # 处理参数
 211:     parser.add_argument(
 212:         '--parallel', action='store_true',
 213:         help='启用并行处理'
 214:     )
 215:     parser.add_argument(
 216:         '--max-memory', type=float, default=4.0,
 217:         help='最大内存使用量 (GB，默认: 4.0)'
 218:     )
 219:     parser.add_argument(
 220:         '--chunk-size', type=int, default=1024,
 221:         help='块大小 (默认: 1024)'
 222:     )
 223:     
 224:     # 其他参数
 225:     parser.add_argument(
 226:         '-v', '--verbose', action='store_true',
 227:         help='详细输出'
 228:     )
 229:     parser.add_argument(
 230:         '--version', action='version', version='北斗网格转换工具 v2.0'
 231:     )
 232:     
 233:     return parser
 234: 
 235: 
 236: def main():
 237:     """主函数"""
 238:     parser = create_parser()
 239:     args = parser.parse_args()
 240:     
 241:     # 设置日志
 242:     setup_logging(args.verbose if hasattr(args, 'verbose') else False)
 243:     
 244:     # 检查模式
 245:     if args.cli:
 246:         # 命令行模式
 247:         if not args.input:
 248:             print("错误: 命令行模式需要指定输入文件 (-i/--input)")
 249:             parser.print_help()
 250:             sys.exit(1)
 251:         
 252:         run_cli(args)
 253:     else:
 254:         # GUI模式
 255:         run_gui()
 256: 
 257: 
 258: if __name__ == '__main__':
 259:     main()
 260: 


================================================================================
文件 18: test_basic.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 基本功能测试脚本
   4: """
   5: 
   6: import os
   7: import sys
   8: import numpy as np
   9: import tempfile
  10: import shutil
  11: 
  12: # 添加项目根目录到路径
  13: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  14: 
  15: from core.grid_calculator import BeidouGridCalculator
  16: from core.aggregation import AggregationMethod, DataAggregator
  17: from core.coordinate_utils import CoordinateUtils
  18: # from data.data_validator import DataValidator  # 暂时注释掉，避免rasterio依赖
  19: 
  20: 
  21: def test_grid_calculator():
  22:     """测试网格计算器"""
  23:     print("测试网格计算器...")
  24:     
  25:     calculator = BeidouGridCalculator()
  26:     
  27:     # 测试网格编码计算
  28:     lat, lon = 39.9042, 116.4074  # 北京天安门
  29:     level = 7
  30:     
  31:     grid_code = calculator.calculate_grid_code(lat, lon, level)
  32:     print(f"北京天安门 ({lat}, {lon}) 的{level}级网格编码: {grid_code}")
  33:     
  34:     # 测试网格尺寸
  35:     lon_size, lat_size = calculator.get_grid_size(level)
  36:     print(f"{level}级网格尺寸: 经度 {lon_size:.6f}°, 纬度 {lat_size:.6f}°")
  37:     
  38:     # 测试网格维度计算
  39:     min_lon, min_lat = 116.0, 39.5
  40:     max_lon, max_lat = 117.0, 40.5
  41:     rows, cols = calculator.calculate_grid_dimensions(min_lon, min_lat, max_lon, max_lat, level)
  42:     print(f"区域 ({min_lon}, {min_lat}) 到 ({max_lon}, {max_lat}) 的{level}级网格维度: {rows}x{cols}")
  43:     
  44:     print("✓ 网格计算器测试通过\n")
  45: 
  46: 
  47: def test_aggregation():
  48:     """测试数据聚合"""
  49:     print("测试数据聚合...")
  50:     
  51:     # 创建测试数据
  52:     data = np.random.rand(100, 100) * 100
  53:     data[50:60, 50:60] = np.nan  # 添加一些无效值
  54:     
  55:     aggregator = DataAggregator(method="mean", window_size=5)
  56:     
  57:     # 测试聚合
  58:     result = aggregator.aggregate(data, nodata_value=np.nan)
  59:     print(f"原始数据形状: {data.shape}")
  60:     print(f"聚合后形状: {result.shape}")
  61:     print(f"原始数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
  62:     print(f"聚合后范围: {np.nanmin(result):.2f} - {np.nanmax(result):.2f}")
  63:     
  64:     # 测试不同聚合方法
  65:     methods = ["max", "min", "mean", "median"]
  66:     for method in methods:
  67:         agg = DataAggregator(method=method, window_size=3)
  68:         result = agg.aggregate(data[:10, :10])  # 小数据测试
  69:         print(f"{method}聚合结果均值: {np.nanmean(result):.2f}")
  70:     
  71:     print("✓ 数据聚合测试通过\n")
  72: 
  73: 
  74: def test_coordinate_utils():
  75:     """测试坐标工具"""
  76:     print("测试坐标工具...")
  77:     
  78:     # 测试坐标格式转换
  79:     lat_dd = 39.9042
  80:     lon_dd = 116.4074
  81:     
  82:     lat_dms = CoordinateUtils.dd_to_dms(lat_dd)
  83:     lon_dms = CoordinateUtils.dd_to_dms(lon_dd)
  84:     print(f"十进制度数: {lat_dd}, {lon_dd}")
  85:     print(f"度分秒格式: {lat_dms}, {lon_dms}")
  86:     
  87:     # 转换回来验证
  88:     lat_dd_back = CoordinateUtils.dms_to_dd(*lat_dms)
  89:     lon_dd_back = CoordinateUtils.dms_to_dd(*lon_dms)
  90:     print(f"转换回十进制: {lat_dd_back:.4f}, {lon_dd_back:.4f}")
  91:     
  92:     # 测试距离计算
  93:     lat1, lon1 = 39.9042, 116.4074  # 北京
  94:     lat2, lon2 = 31.2304, 121.4737  # 上海
  95:     distance = CoordinateUtils.haversine_distance(lat1, lon1, lat2, lon2)
  96:     print(f"北京到上海距离: {distance:.2f} km")
  97:     
  98:     # 测试边界计算
  99:     bounds = CoordinateUtils.calculate_bounds(lat_dd, lon_dd, 1000)  # 1km范围
 100:     print(f"1km范围边界: {bounds}")
 101:     
 102:     print("✓ 坐标工具测试通过\n")
 103: 
 104: 
 105: def test_data_validator():
 106:     """测试数据验证"""
 107:     print("测试数据验证...")
 108:     print("⚠ 跳过数据验证测试 (需要rasterio依赖)")
 109:     print("✓ 数据验证测试跳过\n")
 110: 
 111: 
 112: def test_integration():
 113:     """集成测试"""
 114:     print("集成测试...")
 115:     
 116:     try:
 117:         # 创建临时目录
 118:         temp_dir = tempfile.mkdtemp()
 119:         print(f"临时目录: {temp_dir}")
 120:         
 121:         # 创建测试数据
 122:         data = np.random.rand(50, 50) * 100
 123:         
 124:         # 测试完整流程
 125:         calculator = BeidouGridCalculator()
 126:         aggregator = DataAggregator(method="mean", window_size=3)
 127:         
 128:         # 聚合数据
 129:         aggregated = aggregator.aggregate(data)
 130:         print(f"聚合完成: {data.shape} -> {aggregated.shape}")
 131:         
 132:         # 计算网格编码
 133:         lat, lon = 39.9, 116.4
 134:         level = 7
 135:         grid_code = calculator.calculate_grid_code(lat, lon, level)
 136:         print(f"网格编码: {grid_code}")
 137:         
 138:         print("✓ 集成测试通过")
 139:         
 140:     except Exception as e:
 141:         print(f"✗ 集成测试失败: {e}")
 142:         raise
 143:     finally:
 144:         # 清理临时目录
 145:         if 'temp_dir' in locals():
 146:             shutil.rmtree(temp_dir, ignore_errors=True)
 147:     
 148:     print()
 149: 
 150: 
 151: def main():
 152:     """主测试函数"""
 153:     print("=" * 50)
 154:     print("北斗网格转换工具 - 基本功能测试")
 155:     print("=" * 50)
 156:     print()
 157:     
 158:     try:
 159:         test_grid_calculator()
 160:         test_aggregation()
 161:         test_coordinate_utils()
 162:         test_data_validator()
 163:         test_integration()
 164:         
 165:         print("=" * 50)
 166:         print("✓ 所有测试通过！")
 167:         print("=" * 50)
 168:         
 169:     except Exception as e:
 170:         print("=" * 50)
 171:         print(f"✗ 测试失败: {e}")
 172:         print("=" * 50)
 173:         import traceback
 174:         traceback.print_exc()
 175:         sys.exit(1)
 176: 
 177: 
 178: if __name__ == '__main__':
 179:     main()
 180: 


================================================================================
文件 19: test_center_and_eta.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 测试窗口居中和时间估算功能
   4: """
   5: 
   6: import tkinter as tk
   7: from tkinter import ttk
   8: import time
   9: import threading
  10: import sys
  11: import os
  12: 
  13: # 添加项目根目录到路径
  14: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  15: 
  16: from gui.progress_dialog import ProgressDialog
  17: from gui.main_window import MainWindow
  18: 
  19: 
  20: def test_window_centering():
  21:     """测试窗口居中功能"""
  22:     print("测试窗口居中功能...")
  23:     
  24:     # 测试主窗口居中
  25:     app = MainWindow()
  26:     
  27:     # 显示主窗口位置信息
  28:     app.root.update_idletasks()
  29:     x = app.root.winfo_x()
  30:     y = app.root.winfo_y()
  31:     width = app.root.winfo_width()
  32:     height = app.root.winfo_height()
  33:     screen_width = app.root.winfo_screenwidth()
  34:     screen_height = app.root.winfo_screenheight()
  35:     
  36:     print(f"主窗口位置: ({x}, {y})")
  37:     print(f"主窗口大小: {width}x{height}")
  38:     print(f"屏幕大小: {screen_width}x{screen_height}")
  39:     print(f"是否居中: X={abs(x - (screen_width-width)//2) < 10}, Y={abs(y - (screen_height-height)//2) < 10}")
  40:     
  41:     def test_progress_dialog():
  42:         """测试进度对话框居中"""
  43:         dialog = ProgressDialog(app.root)
  44:         dialog.show()
  45:         
  46:         # 显示对话框位置信息
  47:         dialog.dialog.update_idletasks()
  48:         dx = dialog.dialog.winfo_x()
  49:         dy = dialog.dialog.winfo_y()
  50:         dwidth = dialog.dialog.winfo_width()
  51:         dheight = dialog.dialog.winfo_height()
  52:         
  53:         print(f"\n进度对话框位置: ({dx}, {dy})")
  54:         print(f"进度对话框大小: {dwidth}x{dheight}")
  55:         print(f"对话框是否居中: X={abs(dx - (screen_width-dwidth)//2) < 10}, Y={abs(dy - (screen_height-dheight)//2) < 10}")
  56:         
  57:         def simulate_progress():
  58:             """模拟进度更新"""
  59:             stages = [
  60:                 ("初始化", 0, 10),
  61:                 ("读取数据", 10, 30),
  62:                 ("处理数据", 30, 70),
  63:                 ("聚合数据", 70, 90),
  64:                 ("写入结果", 90, 100)
  65:             ]
  66:             
  67:             for stage_name, start_percent, end_percent in stages:
  68:                 print(f"\n开始阶段: {stage_name}")
  69:                 for percent in range(start_percent, end_percent + 1, 2):
  70:                     if dialog.is_cancelled:
  71:                         break
  72:                     
  73:                     detail = f"正在{stage_name}... ({percent}%)"
  74:                     dialog.update_progress(percent, stage_name, detail)
  75:                     time.sleep(0.3)  # 模拟处理时间
  76:                 
  77:                 if dialog.is_cancelled:
  78:                     break
  79:             
  80:             if not dialog.is_cancelled:
  81:                 time.sleep(2)  # 显示完成状态
  82:                 dialog.close()
  83:         
  84:         # 设置取消回调
  85:         dialog.set_cancel_callback(lambda: print("用户取消了处理"))
  86:         
  87:         # 在新线程中运行进度模拟
  88:         thread = threading.Thread(target=simulate_progress)
  89:         thread.daemon = True
  90:         thread.start()
  91:     
  92:     # 添加测试按钮
  93:     test_frame = ttk.Frame(app.root)
  94:     test_frame.pack(pady=20)
  95:     
  96:     ttk.Button(
  97:         test_frame,
  98:         text="测试进度对话框居中和时间估算",
  99:         command=test_progress_dialog,
 100:         width=30
 101:     ).pack()
 102:     
 103:     app.run()
 104: 
 105: 
 106: def test_time_estimation_accuracy():
 107:     """测试时间估算准确性"""
 108:     root = tk.Tk()
 109:     root.title("时间估算准确性测试")
 110:     root.geometry("800x600")
 111:     
 112:     # 居中显示
 113:     screen_width = root.winfo_screenwidth()
 114:     screen_height = root.winfo_screenheight()
 115:     x = (screen_width - 800) // 2
 116:     y = (screen_height - 600) // 2
 117:     root.geometry(f"800x600+{x}+{y}")
 118:     
 119:     frame = ttk.Frame(root, padding="20")
 120:     frame.pack(fill=tk.BOTH, expand=True)
 121:     
 122:     ttk.Label(
 123:         frame,
 124:         text="时间估算准确性测试",
 125:         font=("Arial", 16, "bold")
 126:     ).pack(pady=(0, 20))
 127:     
 128:     def test_fast_progress():
 129:         """测试快速进度"""
 130:         dialog = ProgressDialog(root)
 131:         dialog.show()
 132:         
 133:         def fast_task():
 134:             for i in range(101):
 135:                 if dialog.is_cancelled:
 136:                     break
 137:                 dialog.update_progress(i, f"快速任务", f"步骤 {i}/100")
 138:                 time.sleep(0.05)  # 快速进度
 139:             
 140:             if not dialog.is_cancelled:
 141:                 time.sleep(1)
 142:                 dialog.close()
 143:         
 144:         dialog.set_cancel_callback(lambda: print("快速任务被取消"))
 145:         threading.Thread(target=fast_task, daemon=True).start()
 146:     
 147:     def test_slow_progress():
 148:         """测试慢速进度"""
 149:         dialog = ProgressDialog(root)
 150:         dialog.show()
 151:         
 152:         def slow_task():
 153:             for i in range(101):
 154:                 if dialog.is_cancelled:
 155:                     break
 156:                 dialog.update_progress(i, f"慢速任务", f"步骤 {i}/100")
 157:                 time.sleep(0.2)  # 慢速进度
 158:             
 159:             if not dialog.is_cancelled:
 160:                 time.sleep(1)
 161:                 dialog.close()
 162:         
 163:         dialog.set_cancel_callback(lambda: print("慢速任务被取消"))
 164:         threading.Thread(target=slow_task, daemon=True).start()
 165:     
 166:     def test_variable_speed():
 167:         """测试变速进度"""
 168:         dialog = ProgressDialog(root)
 169:         dialog.show()
 170:         
 171:         def variable_task():
 172:             # 第一阶段：快速
 173:             for i in range(0, 30):
 174:                 if dialog.is_cancelled:
 175:                     break
 176:                 dialog.update_progress(i, "快速阶段", f"快速处理 {i}/30")
 177:                 time.sleep(0.05)
 178:             
 179:             # 第二阶段：慢速
 180:             for i in range(30, 70):
 181:                 if dialog.is_cancelled:
 182:                     break
 183:                 dialog.update_progress(i, "慢速阶段", f"仔细处理 {i}/70")
 184:                 time.sleep(0.3)
 185:             
 186:             # 第三阶段：中等速度
 187:             for i in range(70, 101):
 188:                 if dialog.is_cancelled:
 189:                     break
 190:                 dialog.update_progress(i, "收尾阶段", f"最后处理 {i}/100")
 191:                 time.sleep(0.1)
 192:             
 193:             if not dialog.is_cancelled:
 194:                 time.sleep(1)
 195:                 dialog.close()
 196:         
 197:         dialog.set_cancel_callback(lambda: print("变速任务被取消"))
 198:         threading.Thread(target=variable_task, daemon=True).start()
 199:     
 200:     # 测试按钮
 201:     button_frame = ttk.Frame(frame)
 202:     button_frame.pack(pady=20)
 203:     
 204:     ttk.Button(
 205:         button_frame,
 206:         text="快速进度测试",
 207:         command=test_fast_progress,
 208:         width=20
 209:     ).pack(side=tk.LEFT, padx=10)
 210:     
 211:     ttk.Button(
 212:         button_frame,
 213:         text="慢速进度测试",
 214:         command=test_slow_progress,
 215:         width=20
 216:     ).pack(side=tk.LEFT, padx=10)
 217:     
 218:     ttk.Button(
 219:         button_frame,
 220:         text="变速进度测试",
 221:         command=test_variable_speed,
 222:         width=20
 223:     ).pack(side=tk.LEFT, padx=10)
 224:     
 225:     ttk.Label(
 226:         frame,
 227:         text="观察要点：",
 228:         font=("Arial", 12, "bold")
 229:     ).pack(anchor=tk.W, pady=(30, 10))
 230:     
 231:     observations = [
 232:         "• 进度达到5%后开始显示预计剩余时间",
 233:         "• 不同阶段的时间估算会根据当前速度调整",
 234:         "• 变速任务中时间估算应该适应速度变化",
 235:         "• 所有对话框都应该在屏幕中央显示"
 236:     ]
 237:     
 238:     for obs in observations:
 239:         ttk.Label(
 240:             frame,
 241:             text=obs,
 242:             font=("Arial", 10)
 243:         ).pack(anchor=tk.W, pady=2)
 244:     
 245:     ttk.Button(
 246:         frame,
 247:         text="退出",
 248:         command=root.quit,
 249:         width=20
 250:     ).pack(pady=(30, 0))
 251:     
 252:     root.mainloop()
 253: 
 254: 
 255: def main():
 256:     """主函数"""
 257:     print("=" * 60)
 258:     print("窗口居中和时间估算功能测试")
 259:     print("=" * 60)
 260:     
 261:     choice = input("""
 262: 请选择测试项目:
 263: 1. 测试窗口居中功能（主窗口和进度对话框）
 264: 2. 测试时间估算准确性
 265: 3. 退出
 266: 
 267: 请输入选择 (1-3): """).strip()
 268:     
 269:     if choice == "1":
 270:         print("启动窗口居中功能测试...")
 271:         test_window_centering()
 272:     elif choice == "2":
 273:         print("启动时间估算准确性测试...")
 274:         test_time_estimation_accuracy()
 275:     elif choice == "3":
 276:         print("退出测试")
 277:     else:
 278:         print("无效选择")
 279: 
 280: 
 281: if __name__ == '__main__':
 282:     main()
 283: 


================================================================================
文件 20: test_completion_fix.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 测试处理完成后取消按钮的修复
   4: """
   5: 
   6: import tkinter as tk
   7: from tkinter import ttk
   8: import time
   9: import threading
  10: import sys
  11: import os
  12: 
  13: # 添加项目根目录到路径
  14: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  15: 
  16: from gui.progress_dialog import ProgressDialog
  17: 
  18: 
  19: def test_completion_cancel_fix():
  20:     """测试处理完成后取消按钮的修复"""
  21:     root = tk.Tk()
  22:     root.title("处理完成后取消按钮修复测试")
  23:     root.geometry("700x500")
  24:     
  25:     def start_completion_test():
  26:         """开始完成测试"""
  27:         dialog = ProgressDialog(root)
  28:         dialog.show()
  29:         
  30:         def simulate_quick_completion():
  31:             """模拟快速完成的处理"""
  32:             try:
  33:                 steps = [
  34:                     (10, "初始化", "正在初始化系统..."),
  35:                     (30, "读取数据", "正在读取输入文件..."),
  36:                     (50, "处理数据", "正在执行数据处理..."),
  37:                     (70, "计算结果", "正在计算最终结果..."),
  38:                     (90, "写入文件", "正在写入输出文件..."),
  39:                     (100, "处理完成", "所有步骤已完成！")
  40:                 ]
  41:                 
  42:                 for percent, message, detail in steps:
  43:                     if dialog.is_cancelled:
  44:                         break
  45:                     
  46:                     dialog.update_progress(percent, message, detail)
  47:                     time.sleep(0.8)  # 快速完成
  48:                 
  49:                 if not dialog.is_cancelled:
  50:                     # 模拟处理器中的等待时间
  51:                     time.sleep(1.5)
  52:                     print("处理完成，对话框应该自动关闭")
  53:                 
  54:                 dialog.close()
  55:                 
  56:             except Exception as e:
  57:                 print(f"测试出错: {e}")
  58:                 dialog.close()
  59:         
  60:         # 设置取消回调
  61:         def on_cancel():
  62:             print("用户尝试取消处理！")
  63:         
  64:         dialog.set_cancel_callback(on_cancel)
  65:         
  66:         # 在新线程中运行模拟处理
  67:         thread = threading.Thread(target=simulate_quick_completion)
  68:         thread.daemon = True
  69:         thread.start()
  70:     
  71:     def start_slow_completion_test():
  72:         """开始慢速完成测试"""
  73:         dialog = ProgressDialog(root)
  74:         dialog.show()
  75:         
  76:         def simulate_slow_completion():
  77:             """模拟慢速完成的处理"""
  78:             try:
  79:                 # 快速到达99%
  80:                 for i in range(0, 100, 10):
  81:                     if dialog.is_cancelled:
  82:                         break
  83:                     dialog.update_progress(i, f"处理步骤 {i//10 + 1}", f"当前进度: {i}%")
  84:                     time.sleep(0.2)
  85:                 
  86:                 if not dialog.is_cancelled:
  87:                     # 在99%停留较长时间
  88:                     dialog.update_progress(99, "即将完成", "正在进行最后的清理工作...")
  89:                     time.sleep(3)  # 在99%停留3秒
  90:                     
  91:                     # 然后完成
  92:                     dialog.update_progress(100, "处理完成", "所有工作已完成！")
  93:                     time.sleep(1.5)  # 显示完成信息
  94:                 
  95:                 dialog.close()
  96:                 
  97:             except Exception as e:
  98:                 print(f"测试出错: {e}")
  99:                 dialog.close()
 100:         
 101:         # 设置取消回调
 102:         def on_cancel():
 103:             print("用户在接近完成时尝试取消！")
 104:         
 105:         dialog.set_cancel_callback(on_cancel)
 106:         
 107:         # 在新线程中运行模拟处理
 108:         thread = threading.Thread(target=simulate_slow_completion)
 109:         thread.daemon = True
 110:         thread.start()
 111:     
 112:     # 创建测试界面
 113:     frame = ttk.Frame(root, padding="30")
 114:     frame.pack(fill=tk.BOTH, expand=True)
 115:     
 116:     ttk.Label(
 117:         frame, 
 118:         text="处理完成后取消按钮修复测试", 
 119:         font=("Arial", 18, "bold")
 120:     ).pack(pady=(0, 30))
 121:     
 122:     ttk.Label(
 123:         frame,
 124:         text="修复内容：",
 125:         font=("Arial", 12, "bold")
 126:     ).pack(anchor=tk.W, pady=(0, 10))
 127:     
 128:     fixes = [
 129:         "• 进度达到100%时立即禁用取消按钮",
 130:         "• 取消按钮文本变为'处理完成'",
 131:         "• 处理完成后1.5秒自动关闭对话框",
 132:         "• 防止用户在完成后误点取消"
 133:     ]
 134:     
 135:     for fix in fixes:
 136:         ttk.Label(
 137:             frame,
 138:             text=fix,
 139:             font=("Arial", 10)
 140:         ).pack(anchor=tk.W, pady=2)
 141:     
 142:     ttk.Label(
 143:         frame,
 144:         text="\n测试说明：",
 145:         font=("Arial", 12, "bold")
 146:     ).pack(anchor=tk.W, pady=(20, 10))
 147:     
 148:     instructions = [
 149:         "测试1 - 快速完成：观察进度达到100%时取消按钮是否立即禁用",
 150:         "测试2 - 慢速完成：在99%时尝试点击取消，然后观察100%时的行为"
 151:     ]
 152:     
 153:     for i, instruction in enumerate(instructions, 1):
 154:         ttk.Label(
 155:             frame,
 156:             text=f"{i}. {instruction}",
 157:             font=("Arial", 10),
 158:             wraplength=600
 159:         ).pack(anchor=tk.W, pady=2)
 160:     
 161:     # 测试按钮
 162:     button_frame = ttk.Frame(frame)
 163:     button_frame.pack(pady=(30, 0))
 164:     
 165:     ttk.Button(
 166:         button_frame,
 167:         text="测试1: 快速完成",
 168:         command=start_completion_test,
 169:         width=20
 170:     ).pack(side=tk.LEFT, padx=(0, 10))
 171:     
 172:     ttk.Button(
 173:         button_frame,
 174:         text="测试2: 慢速完成",
 175:         command=start_slow_completion_test,
 176:         width=20
 177:     ).pack(side=tk.LEFT, padx=10)
 178:     
 179:     ttk.Button(
 180:         button_frame,
 181:         text="退出",
 182:         command=root.quit,
 183:         width=20
 184:     ).pack(side=tk.LEFT, padx=(10, 0))
 185:     
 186:     root.mainloop()
 187: 
 188: 
 189: def test_cancel_button_states():
 190:     """测试取消按钮的各种状态"""
 191:     root = tk.Tk()
 192:     root.title("取消按钮状态测试")
 193:     root.geometry("600x400")
 194:     
 195:     def test_normal_cancel():
 196:         """测试正常取消"""
 197:         dialog = ProgressDialog(root)
 198:         dialog.show()
 199:         
 200:         def slow_task():
 201:             for i in range(101):
 202:                 if dialog.is_cancelled:
 203:                     print("任务被正常取消")
 204:                     break
 205:                 dialog.update_progress(i, f"执行中 {i}%", f"步骤 {i}/100")
 206:                 time.sleep(0.1)
 207:             dialog.close()
 208:         
 209:         dialog.set_cancel_callback(lambda: print("取消回调被调用"))
 210:         threading.Thread(target=slow_task, daemon=True).start()
 211:     
 212:     def test_completion_cancel():
 213:         """测试完成时的取消按钮"""
 214:         dialog = ProgressDialog(root)
 215:         dialog.show()
 216:         
 217:         def complete_task():
 218:             for i in range(101):
 219:                 dialog.update_progress(i, f"执行中 {i}%", f"步骤 {i}/100")
 220:                 time.sleep(0.05)
 221:             
 222:             # 完成后等待，观察取消按钮状态
 223:             time.sleep(3)
 224:             dialog.close()
 225:         
 226:         dialog.set_cancel_callback(lambda: print("尝试在完成后取消"))
 227:         threading.Thread(target=complete_task, daemon=True).start()
 228:     
 229:     # 创建测试界面
 230:     frame = ttk.Frame(root, padding="30")
 231:     frame.pack(fill=tk.BOTH, expand=True)
 232:     
 233:     ttk.Label(
 234:         frame, 
 235:         text="取消按钮状态测试", 
 236:         font=("Arial", 18, "bold")
 237:     ).pack(pady=(0, 30))
 238:     
 239:     ttk.Label(
 240:         frame,
 241:         text="测试项目：",
 242:         font=("Arial", 12, "bold")
 243:     ).pack(anchor=tk.W, pady=(0, 10))
 244:     
 245:     tests = [
 246:         "1. 正常取消：在处理过程中点击取消按钮",
 247:         "2. 完成时取消：观察进度100%时取消按钮的状态变化"
 248:     ]
 249:     
 250:     for test in tests:
 251:         ttk.Label(
 252:             frame,
 253:             text=test,
 254:             font=("Arial", 10)
 255:         ).pack(anchor=tk.W, pady=2)
 256:     
 257:     # 按钮
 258:     button_frame = ttk.Frame(frame)
 259:     button_frame.pack(pady=(30, 0))
 260:     
 261:     ttk.Button(
 262:         button_frame,
 263:         text="测试正常取消",
 264:         command=test_normal_cancel,
 265:         width=20
 266:     ).pack(side=tk.LEFT, padx=(0, 10))
 267:     
 268:     ttk.Button(
 269:         button_frame,
 270:         text="测试完成时取消",
 271:         command=test_completion_cancel,
 272:         width=20
 273:     ).pack(side=tk.LEFT, padx=10)
 274:     
 275:     ttk.Button(
 276:         button_frame,
 277:         text="退出",
 278:         command=root.quit,
 279:         width=20
 280:     ).pack(side=tk.LEFT, padx=(10, 0))
 281:     
 282:     root.mainloop()
 283: 
 284: 
 285: def main():
 286:     """主函数"""
 287:     print("=" * 60)
 288:     print("处理完成后取消按钮修复测试")
 289:     print("=" * 60)
 290:     
 291:     choice = input("""
 292: 请选择测试项目:
 293: 1. 测试处理完成后的取消按钮修复
 294: 2. 测试取消按钮的各种状态
 295: 3. 退出
 296: 
 297: 请输入选择 (1-3): """).strip()
 298:     
 299:     if choice == "1":
 300:         print("启动处理完成后取消按钮修复测试...")
 301:         test_completion_cancel_fix()
 302:     elif choice == "2":
 303:         print("启动取消按钮状态测试...")
 304:         test_cancel_button_states()
 305:     elif choice == "3":
 306:         print("退出测试")
 307:     else:
 308:         print("无效选择")
 309: 
 310: 
 311: if __name__ == '__main__':
 312:     main()
 313: 


================================================================================
文件 21: test_core.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 核心功能测试脚本 (不依赖地理空间库)
   4: """
   5: 
   6: import os
   7: import sys
   8: import numpy as np
   9: 
  10: # 添加项目根目录到路径
  11: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  12: 
  13: from core.grid_calculator import BeidouGridCalculator
  14: from core.aggregation import AggregationMethod, DataAggregator
  15: from core.coordinate_utils import CoordinateUtils
  16: 
  17: 
  18: def test_grid_calculator():
  19:     """测试网格计算器"""
  20:     print("测试网格计算器...")
  21:     
  22:     calculator = BeidouGridCalculator()
  23:     
  24:     # 测试网格编码计算
  25:     lat, lon = 39.9042, 116.4074  # 北京天安门
  26:     level = 7
  27:     
  28:     grid_code = calculator.calculate_grid_code(lat, lon, level)
  29:     print(f"北京天安门 ({lat}, {lon}) 的{level}级网格编码: {grid_code}")
  30:     
  31:     # 测试网格尺寸
  32:     lon_size, lat_size = calculator.get_grid_size(level)
  33:     print(f"{level}级网格尺寸: 经度 {lon_size:.6f}°, 纬度 {lat_size:.6f}°")
  34:     
  35:     # 测试网格维度计算
  36:     min_lon, min_lat = 116.0, 39.5
  37:     max_lon, max_lat = 117.0, 40.5
  38:     rows, cols = calculator.calculate_grid_dimensions(min_lon, min_lat, max_lon, max_lat, level)
  39:     print(f"区域 ({min_lon}, {min_lat}) 到 ({max_lon}, {max_lat}) 的{level}级网格维度: {rows}x{cols}")
  40:     
  41:     # 测试不同级别
  42:     for test_level in [1, 5, 10]:
  43:         code = calculator.calculate_grid_code(lat, lon, test_level)
  44:         size = calculator.get_grid_size(test_level)
  45:         print(f"级别{test_level}: 编码={code}, 尺寸={size[0]:.6f}°x{size[1]:.6f}°")
  46:     
  47:     print("✓ 网格计算器测试通过\n")
  48: 
  49: 
  50: def test_aggregation():
  51:     """测试数据聚合"""
  52:     print("测试数据聚合...")
  53:     
  54:     # 创建测试数据
  55:     np.random.seed(42)  # 固定随机种子
  56:     data = np.random.rand(20, 20) * 100
  57:     data[10:15, 10:15] = np.nan  # 添加一些无效值
  58:     
  59:     print(f"原始数据形状: {data.shape}")
  60:     print(f"原始数据范围: {np.nanmin(data):.2f} - {np.nanmax(data):.2f}")
  61:     print(f"有效值数量: {np.sum(~np.isnan(data))}")
  62:     
  63:     # 测试不同聚合方法
  64:     methods_to_test = ["max", "min", "mean", "median", "sum"]
  65:     
  66:     for method in methods_to_test:
  67:         try:
  68:             aggregator = DataAggregator(method=method, window_size=3)
  69:             result = aggregator.aggregate(data, nodata_value=np.nan)
  70:             
  71:             print(f"{method}聚合:")
  72:             print(f"  结果形状: {result.shape}")
  73:             print(f"  结果范围: {np.nanmin(result):.2f} - {np.nanmax(result):.2f}")
  74:             print(f"  有效值数量: {np.sum(~np.isnan(result))}")
  75:             
  76:         except Exception as e:
  77:             print(f"  {method}聚合失败: {e}")
  78:     
  79:     # 测试不同窗口大小
  80:     print("\n测试不同窗口大小:")
  81:     for window_size in [3, 5, 7]:
  82:         aggregator = DataAggregator(method="mean", window_size=window_size)
  83:         result = aggregator.aggregate(data[:10, :10])  # 小数据测试
  84:         print(f"窗口大小{window_size}: 结果形状{result.shape}, 均值{np.nanmean(result):.2f}")
  85:     
  86:     print("✓ 数据聚合测试通过\n")
  87: 
  88: 
  89: def test_coordinate_utils():
  90:     """测试坐标工具"""
  91:     print("测试坐标工具...")
  92:     
  93:     # 测试坐标格式转换
  94:     lat_dd = 39.9042
  95:     lon_dd = 116.4074
  96:     
  97:     print(f"原始坐标 (十进制度): {lat_dd}, {lon_dd}")
  98:     
  99:     # 转换为度分秒
 100:     lat_dms = CoordinateUtils.degrees_to_dms(lat_dd)
 101:     lon_dms = CoordinateUtils.degrees_to_dms(lon_dd)
 102:     print(f"度分秒格式: {lat_dms}, {lon_dms}")
 103: 
 104:     # 转换回十进制度验证
 105:     lat_dd_back = CoordinateUtils.dms_to_degrees(*lat_dms)
 106:     lon_dd_back = CoordinateUtils.dms_to_degrees(*lon_dms)
 107:     print(f"转换回十进制: {lat_dd_back:.6f}, {lon_dd_back:.6f}")
 108:     
 109:     # 验证精度
 110:     lat_diff = abs(lat_dd - lat_dd_back)
 111:     lon_diff = abs(lon_dd - lon_dd_back)
 112:     print(f"转换误差: 纬度{lat_diff:.8f}°, 经度{lon_diff:.8f}°")
 113:     
 114:     assert lat_diff < 1e-6, f"纬度转换误差过大: {lat_diff}"
 115:     assert lon_diff < 1e-6, f"经度转换误差过大: {lon_diff}"
 116:     
 117:     # 测试距离计算
 118:     print("\n测试距离计算:")
 119:     test_points = [
 120:         ("北京", 39.9042, 116.4074),
 121:         ("上海", 31.2304, 121.4737),
 122:         ("广州", 23.1291, 113.2644),
 123:         ("深圳", 22.5431, 114.0579)
 124:     ]
 125:     
 126:     for i, (name1, lat1, lon1) in enumerate(test_points):
 127:         for j, (name2, lat2, lon2) in enumerate(test_points):
 128:             if i < j:  # 避免重复计算
 129:                 distance = CoordinateUtils.calculate_distance(lat1, lon1, lat2, lon2)
 130:                 print(f"{name1}到{name2}距离: {distance/1000:.2f} km")  # 转换为km
 131:     
 132:     # 测试边界计算
 133:     print("\n测试边界计算:")
 134:     lat, lon = 39.9042, 116.4074
 135:     # 测试网格边界计算（使用网格级别而不是距离）
 136:     for level in [5, 7, 9]:
 137:         bounds = CoordinateUtils.calculate_bounds(lat, lon, level)
 138:         print(f"级别{level}网格边界: {bounds}")
 139: 
 140:         # 验证边界合理性
 141:         min_lon, min_lat, max_lon, max_lat = bounds
 142:         assert min_lon < lon < max_lon, f"经度边界错误: {bounds}"
 143:         assert min_lat < lat < max_lat, f"纬度边界错误: {bounds}"
 144:     
 145:     print("✓ 坐标工具测试通过\n")
 146: 
 147: 
 148: def test_integration():
 149:     """集成测试"""
 150:     print("集成测试...")
 151:     
 152:     try:
 153:         # 模拟完整的处理流程
 154:         print("1. 初始化组件...")
 155:         calculator = BeidouGridCalculator()
 156:         aggregator = DataAggregator(method="mean", window_size=5)
 157:         
 158:         # 创建模拟数据
 159:         print("2. 创建测试数据...")
 160:         np.random.seed(123)
 161:         data = np.random.rand(50, 50) * 100
 162:         
 163:         # 添加一些空间模式
 164:         for i in range(10, 40):
 165:             for j in range(10, 40):
 166:                 data[i, j] += 50 * np.exp(-((i-25)**2 + (j-25)**2) / 100)
 167:         
 168:         print(f"   数据形状: {data.shape}")
 169:         print(f"   数据范围: {np.min(data):.2f} - {np.max(data):.2f}")
 170:         
 171:         # 数据聚合
 172:         print("3. 执行数据聚合...")
 173:         aggregated = aggregator.aggregate(data)
 174:         print(f"   聚合后形状: {aggregated.shape}")
 175:         print(f"   聚合后范围: {np.nanmin(aggregated):.2f} - {np.nanmax(aggregated):.2f}")
 176:         
 177:         # 网格编码计算
 178:         print("4. 计算网格编码...")
 179:         test_coords = [
 180:             (39.9, 116.4),  # 北京
 181:             (31.2, 121.5),  # 上海
 182:             (23.1, 113.3),  # 广州
 183:         ]
 184:         
 185:         for lat, lon in test_coords:
 186:             for level in [5, 7, 9]:
 187:                 code = calculator.calculate_grid_code(lat, lon, level)
 188:                 print(f"   ({lat}, {lon}) 级别{level}: {code}")
 189:         
 190:         # 网格维度计算
 191:         print("5. 计算网格维度...")
 192:         bounds = (116.0, 39.0, 117.0, 40.0)  # 北京区域
 193:         for level in [6, 7, 8]:
 194:             rows, cols = calculator.calculate_grid_dimensions(*bounds, level)
 195:             total_grids = rows * cols
 196:             print(f"   级别{level}: {rows}x{cols} = {total_grids}个网格")
 197:         
 198:         print("✓ 集成测试通过")
 199:         
 200:     except Exception as e:
 201:         print(f"✗ 集成测试失败: {e}")
 202:         import traceback
 203:         traceback.print_exc()
 204:         raise
 205:     
 206:     print()
 207: 
 208: 
 209: def main():
 210:     """主测试函数"""
 211:     print("=" * 60)
 212:     print("北斗网格转换工具 - 核心功能测试")
 213:     print("=" * 60)
 214:     print()
 215:     
 216:     try:
 217:         test_grid_calculator()
 218:         test_aggregation()
 219:         test_coordinate_utils()
 220:         test_integration()
 221:         
 222:         print("=" * 60)
 223:         print("✓ 所有核心功能测试通过！")
 224:         print("=" * 60)
 225:         print()
 226:         print("注意: 完整功能测试需要安装以下依赖:")
 227:         print("  pip install rasterio fiona shapely pyproj")
 228:         print()
 229:         
 230:     except Exception as e:
 231:         print("=" * 60)
 232:         print(f"✗ 测试失败: {e}")
 233:         print("=" * 60)
 234:         import traceback
 235:         traceback.print_exc()
 236:         sys.exit(1)
 237: 
 238: 
 239: if __name__ == '__main__':
 240:     main()
 241: 


================================================================================
文件 22: test_detailed_progress.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 测试详细进度显示和取消功能
   4: """
   5: 
   6: import tkinter as tk
   7: from tkinter import ttk
   8: import time
   9: import threading
  10: import sys
  11: import os
  12: import numpy as np
  13: 
  14: # 添加项目根目录到路径
  15: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  16: 
  17: from gui.progress_dialog import ProgressDialog
  18: from core.aggregation import DataAggregator, AggregationMethod
  19: 
  20: 
  21: def test_detailed_aggregation_progress():
  22:     """测试详细的聚合进度显示"""
  23:     root = tk.Tk()
  24:     root.title("详细聚合进度测试")
  25:     root.geometry("600x400")
  26:     
  27:     def start_aggregation_test():
  28:         """开始聚合测试"""
  29:         dialog = ProgressDialog(root)
  30:         dialog.show()
  31:         
  32:         def simulate_aggregation():
  33:             """模拟聚合处理"""
  34:             try:
  35:                 # 创建测试数据
  36:                 test_data = np.random.rand(200, 200) * 100  # 200x200的测试数据
  37:                 
  38:                 # 创建聚合器
  39:                 aggregator = DataAggregator(
  40:                     method=AggregationMethod.MEAN,
  41:                     window_size=5,
  42:                     boundary_handling="constant"
  43:                 )
  44:                 
  45:                 # 模拟进度回调
  46:                 def progress_callback(percent, message, detail):
  47:                     if not dialog.is_cancelled:
  48:                         dialog.update_progress(percent, message, detail)
  49:                 
  50:                 # 模拟取消检查
  51:                 def cancel_check():
  52:                     if dialog.is_cancelled:
  53:                         raise InterruptedError("处理已被用户取消")
  54:                 
  55:                 # 执行聚合（这会显示详细进度）
  56:                 dialog.update_progress(0, "开始数据聚合", "初始化聚合器...")
  57:                 time.sleep(0.5)
  58:                 
  59:                 dialog.update_progress(10, "准备数据", f"数据尺寸: {test_data.shape}")
  60:                 time.sleep(0.5)
  61:                 
  62:                 # 执行实际聚合
  63:                 result = aggregator.aggregate(test_data, progress_callback=progress_callback, cancel_check=cancel_check)
  64:                 
  65:                 if not dialog.is_cancelled:
  66:                     dialog.update_progress(100, "聚合完成", f"输出尺寸: {result.shape}")
  67:                     time.sleep(2)
  68:                 
  69:             except InterruptedError:
  70:                 dialog.update_progress(0, "处理已取消", "用户取消了聚合操作")
  71:                 time.sleep(1)
  72:             except Exception as e:
  73:                 dialog.update_progress(0, "处理失败", f"错误: {str(e)}")
  74:                 time.sleep(2)
  75:             finally:
  76:                 if not dialog.is_cancelled:
  77:                     dialog.close()
  78:         
  79:         # 设置取消回调
  80:         def on_cancel():
  81:             print("用户取消了聚合处理！")
  82:         
  83:         dialog.set_cancel_callback(on_cancel)
  84:         
  85:         # 在新线程中运行聚合
  86:         thread = threading.Thread(target=simulate_aggregation)
  87:         thread.daemon = True
  88:         thread.start()
  89:     
  90:     # 创建测试界面
  91:     frame = ttk.Frame(root, padding="30")
  92:     frame.pack(fill=tk.BOTH, expand=True)
  93:     
  94:     ttk.Label(
  95:         frame, 
  96:         text="详细聚合进度测试", 
  97:         font=("Arial", 18, "bold")
  98:     ).pack(pady=(0, 30))
  99:     
 100:     ttk.Label(
 101:         frame,
 102:         text="测试特性：",
 103:         font=("Arial", 12, "bold")
 104:     ).pack(anchor=tk.W, pady=(0, 10))
 105:     
 106:     features = [
 107:         "• 实时显示聚合进度（每1000像素更新）",
 108:         "• 显示已处理像素数量和百分比",
 109:         "• 支持取消操作和立即响应",
 110:         "• 详细的状态信息显示",
 111:         "• 改进的取消按钮行为"
 112:     ]
 113:     
 114:     for feature in features:
 115:         ttk.Label(
 116:             frame,
 117:             text=feature,
 118:             font=("Arial", 10)
 119:         ).pack(anchor=tk.W, pady=2)
 120:     
 121:     ttk.Button(
 122:         frame,
 123:         text="开始聚合进度测试",
 124:         command=start_aggregation_test,
 125:         width=30
 126:     ).pack(pady=(30, 10))
 127:     
 128:     ttk.Button(
 129:         frame,
 130:         text="退出",
 131:         command=root.quit,
 132:         width=30
 133:     ).pack(pady=10)
 134:     
 135:     root.mainloop()
 136: 
 137: 
 138: def test_cancel_functionality():
 139:     """测试取消功能"""
 140:     root = tk.Tk()
 141:     root.title("取消功能测试")
 142:     root.geometry("600x400")
 143:     
 144:     def start_cancel_test():
 145:         """开始取消测试"""
 146:         dialog = ProgressDialog(root)
 147:         dialog.show()
 148:         
 149:         def long_running_task():
 150:             """长时间运行的任务"""
 151:             try:
 152:                 for i in range(100):
 153:                     if dialog.is_cancelled:
 154:                         raise InterruptedError("任务已被取消")
 155:                     
 156:                     dialog.update_progress(
 157:                         i + 1, 
 158:                         f"执行步骤 {i + 1}/100", 
 159:                         f"当前进度: {i + 1}%，预计剩余时间: {100 - i - 1} 秒"
 160:                     )
 161:                     time.sleep(1)  # 模拟耗时操作
 162:                 
 163:                 dialog.update_progress(100, "任务完成", "所有步骤已完成！")
 164:                 time.sleep(2)
 165:                 
 166:             except InterruptedError:
 167:                 print("任务被用户取消")
 168:             finally:
 169:                 if not dialog.is_cancelled:
 170:                     dialog.close()
 171:         
 172:         # 设置取消回调
 173:         def on_cancel():
 174:             print("立即取消任务！")
 175:             # 这里可以添加清理逻辑
 176:         
 177:         dialog.set_cancel_callback(on_cancel)
 178:         
 179:         # 在新线程中运行任务
 180:         thread = threading.Thread(target=long_running_task)
 181:         thread.daemon = True
 182:         thread.start()
 183:     
 184:     # 创建测试界面
 185:     frame = ttk.Frame(root, padding="30")
 186:     frame.pack(fill=tk.BOTH, expand=True)
 187:     
 188:     ttk.Label(
 189:         frame, 
 190:         text="取消功能测试", 
 191:         font=("Arial", 18, "bold")
 192:     ).pack(pady=(0, 30))
 193:     
 194:     ttk.Label(
 195:         frame,
 196:         text="说明：",
 197:         font=("Arial", 12, "bold")
 198:     ).pack(anchor=tk.W, pady=(0, 10))
 199:     
 200:     instructions = [
 201:         "1. 点击开始测试按钮启动长时间任务",
 202:         "2. 任务会运行100秒（每秒更新一次进度）",
 203:         "3. 在任务运行期间点击'取消处理'按钮",
 204:         "4. 观察取消按钮是否立即响应",
 205:         "5. 对话框应该立即关闭而不是一直显示'正在取消'"
 206:     ]
 207:     
 208:     for instruction in instructions:
 209:         ttk.Label(
 210:             frame,
 211:             text=instruction,
 212:             font=("Arial", 10)
 213:         ).pack(anchor=tk.W, pady=2)
 214:     
 215:     ttk.Button(
 216:         frame,
 217:         text="开始取消功能测试",
 218:         command=start_cancel_test,
 219:         width=30
 220:     ).pack(pady=(30, 10))
 221:     
 222:     ttk.Button(
 223:         frame,
 224:         text="退出",
 225:         command=root.quit,
 226:         width=30
 227:     ).pack(pady=10)
 228:     
 229:     root.mainloop()
 230: 
 231: 
 232: def main():
 233:     """主函数"""
 234:     print("=" * 60)
 235:     print("详细进度显示和取消功能测试")
 236:     print("=" * 60)
 237:     
 238:     choice = input("""
 239: 请选择测试项目:
 240: 1. 测试详细聚合进度显示
 241: 2. 测试取消功能
 242: 3. 退出
 243: 
 244: 请输入选择 (1-3): """).strip()
 245:     
 246:     if choice == "1":
 247:         print("启动详细聚合进度测试...")
 248:         test_detailed_aggregation_progress()
 249:     elif choice == "2":
 250:         print("启动取消功能测试...")
 251:         test_cancel_functionality()
 252:     elif choice == "3":
 253:         print("退出测试")
 254:     else:
 255:         print("无效选择")
 256: 
 257: 
 258: if __name__ == '__main__':
 259:     main()
 260: 


================================================================================
文件 23: test_gui.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: GUI界面测试脚本
   4: """
   5: 
   6: import tkinter as tk
   7: from tkinter import ttk
   8: import time
   9: import threading
  10: import sys
  11: import os
  12: 
  13: # 添加项目根目录到路径
  14: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  15: 
  16: from gui.progress_dialog import ProgressDialog, IndeterminateProgressDialog
  17: 
  18: 
  19: def test_progress_dialog():
  20:     """测试进度对话框"""
  21:     root = tk.Tk()
  22:     root.title("进度对话框测试")
  23:     root.geometry("400x300")
  24:     
  25:     def test_determinate():
  26:         """测试确定进度对话框"""
  27:         dialog = ProgressDialog(root)
  28:         dialog.show()
  29:         
  30:         def update_progress():
  31:             for i in range(0, 101, 5):
  32:                 if dialog.is_cancelled:
  33:                     break
  34:                 
  35:                 message = f"正在处理第 {i//5 + 1} 步"
  36:                 detail = f"当前进度: {i}%, 剩余时间: {(100-i)//5} 秒"
  37:                 
  38:                 dialog.update_progress(i, message, detail)
  39:                 time.sleep(0.5)
  40:             
  41:             if not dialog.is_cancelled:
  42:                 dialog.update_progress(100, "处理完成", "所有任务已完成！")
  43:                 time.sleep(1)
  44:             
  45:             dialog.close()
  46:         
  47:         thread = threading.Thread(target=update_progress)
  48:         thread.daemon = True
  49:         thread.start()
  50:     
  51:     def test_indeterminate():
  52:         """测试不确定进度对话框"""
  53:         dialog = IndeterminateProgressDialog(root)
  54:         dialog.show()
  55:         
  56:         def update_progress():
  57:             messages = [
  58:                 ("初始化系统", "正在加载配置文件..."),
  59:                 ("连接数据库", "正在建立数据库连接..."),
  60:                 ("验证数据", "正在验证输入数据的完整性..."),
  61:                 ("准备处理", "正在分配系统资源..."),
  62:                 ("执行计算", "正在执行复杂的数学运算..."),
  63:                 ("生成结果", "正在生成最终结果..."),
  64:                 ("清理资源", "正在清理临时文件..."),
  65:             ]
  66:             
  67:             for i, (message, detail) in enumerate(messages):
  68:                 if dialog.is_cancelled:
  69:                     break
  70:                 
  71:                 dialog.update_progress(0, message, detail)
  72:                 time.sleep(2)
  73:             
  74:             if not dialog.is_cancelled:
  75:                 dialog.update_progress(0, "处理完成", "所有任务已成功完成！")
  76:                 time.sleep(1)
  77:             
  78:             dialog.close()
  79:         
  80:         thread = threading.Thread(target=update_progress)
  81:         thread.daemon = True
  82:         thread.start()
  83:     
  84:     # 创建测试按钮
  85:     frame = ttk.Frame(root, padding="20")
  86:     frame.pack(fill=tk.BOTH, expand=True)
  87:     
  88:     ttk.Label(frame, text="进度对话框测试", font=("Arial", 16, "bold")).pack(pady=(0, 20))
  89:     
  90:     ttk.Button(
  91:         frame, 
  92:         text="测试确定进度对话框", 
  93:         command=test_determinate,
  94:         width=25
  95:     ).pack(pady=10)
  96:     
  97:     ttk.Button(
  98:         frame, 
  99:         text="测试不确定进度对话框", 
 100:         command=test_indeterminate,
 101:         width=25
 102:     ).pack(pady=10)
 103:     
 104:     ttk.Button(
 105:         frame, 
 106:         text="退出", 
 107:         command=root.quit,
 108:         width=25
 109:     ).pack(pady=20)
 110:     
 111:     root.mainloop()
 112: 
 113: 
 114: def test_main_window():
 115:     """测试主窗口"""
 116:     try:
 117:         from gui.main_window import MainWindow
 118:         
 119:         app = MainWindow()
 120:         app.run()
 121:         
 122:     except Exception as e:
 123:         print(f"主窗口测试失败: {e}")
 124:         import traceback
 125:         traceback.print_exc()
 126: 
 127: 
 128: def main():
 129:     """主函数"""
 130:     print("=" * 50)
 131:     print("GUI界面测试")
 132:     print("=" * 50)
 133:     
 134:     choice = input("""
 135: 请选择测试项目:
 136: 1. 测试进度对话框
 137: 2. 测试主窗口
 138: 3. 退出
 139: 
 140: 请输入选择 (1-3): """).strip()
 141:     
 142:     if choice == "1":
 143:         print("启动进度对话框测试...")
 144:         test_progress_dialog()
 145:     elif choice == "2":
 146:         print("启动主窗口测试...")
 147:         test_main_window()
 148:     elif choice == "3":
 149:         print("退出测试")
 150:     else:
 151:         print("无效选择")
 152: 
 153: 
 154: if __name__ == '__main__':
 155:     main()
 156: 


================================================================================
文件 24: test_improved_gui.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 测试改进后的GUI界面
   4: """
   5: 
   6: import tkinter as tk
   7: from tkinter import ttk
   8: import time
   9: import threading
  10: import sys
  11: import os
  12: 
  13: # 添加项目根目录到路径
  14: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  15: 
  16: from gui.progress_dialog import ProgressDialog, IndeterminateProgressDialog
  17: 
  18: 
  19: def test_large_progress_dialog():
  20:     """测试大尺寸进度对话框"""
  21:     root = tk.Tk()
  22:     root.title("大尺寸进度对话框测试")
  23:     root.geometry("600x400")
  24:     
  25:     def start_test():
  26:         """开始测试"""
  27:         dialog = ProgressDialog(root)
  28:         dialog.show()
  29:         
  30:         def simulate_processing():
  31:             """模拟处理过程"""
  32:             steps = [
  33:                 (5, "初始化系统", "正在加载配置文件和初始化系统组件..."),
  34:                 (15, "读取输入数据", "正在从磁盘读取大型栅格文件 (1.2GB)，请耐心等待..."),
  35:                 (25, "数据验证", "正在验证数据完整性和格式兼容性，检查坐标系统..."),
  36:                 (35, "内存分配", "正在为大数据处理分配内存缓冲区 (2.5GB)..."),
  37:                 (45, "数据预处理", "正在执行数据清洗、去噪和格式转换操作..."),
  38:                 (55, "网格计算", "正在计算北斗网格编码，当前处理级别7网格..."),
  39:                 (65, "数据聚合", "正在使用均值方法聚合数据，窗口大小: 5x5..."),
  40:                 (75, "空间分析", "正在执行空间插值和边界处理算法..."),
  41:                 (85, "结果生成", "正在生成输出栅格和矢量文件..."),
  42:                 (95, "文件写入", "正在将结果写入磁盘，压缩格式: LZW..."),
  43:                 (100, "处理完成", "所有处理步骤已完成！生成了 15,847 个有效网格单元。")
  44:             ]
  45:             
  46:             for percent, message, detail in steps:
  47:                 if dialog.is_cancelled:
  48:                     break
  49:                 
  50:                 dialog.update_progress(percent, message, detail)
  51:                 time.sleep(1.5)  # 模拟处理时间
  52:             
  53:             if not dialog.is_cancelled:
  54:                 time.sleep(2)  # 显示完成信息
  55:             
  56:             dialog.close()
  57:         
  58:         # 设置取消回调
  59:         def on_cancel():
  60:             print("用户取消了处理！")
  61:             # 这里可以添加清理逻辑
  62:         
  63:         dialog.set_cancel_callback(on_cancel)
  64:         
  65:         # 在新线程中运行模拟处理
  66:         thread = threading.Thread(target=simulate_processing)
  67:         thread.daemon = True
  68:         thread.start()
  69:     
  70:     # 创建测试界面
  71:     frame = ttk.Frame(root, padding="30")
  72:     frame.pack(fill=tk.BOTH, expand=True)
  73:     
  74:     ttk.Label(
  75:         frame, 
  76:         text="改进后的进度对话框测试", 
  77:         font=("Arial", 18, "bold")
  78:     ).pack(pady=(0, 30))
  79:     
  80:     ttk.Label(
  81:         frame,
  82:         text="测试特性：",
  83:         font=("Arial", 12, "bold")
  84:     ).pack(anchor=tk.W, pady=(0, 10))
  85:     
  86:     features = [
  87:         "• 更大的对话框尺寸 (700x400)",
  88:         "• 更长的进度条 (600px)",
  89:         "• 更大的字体和更好的可读性",
  90:         "• 详细的状态信息显示",
  91:         "• 实时时间显示",
  92:         "• 改进的取消功能"
  93:     ]
  94:     
  95:     for feature in features:
  96:         ttk.Label(
  97:             frame,
  98:             text=feature,
  99:             font=("Arial", 10)
 100:         ).pack(anchor=tk.W, pady=2)
 101:     
 102:     ttk.Button(
 103:         frame,
 104:         text="开始测试大尺寸进度对话框",
 105:         command=start_test,
 106:         width=30
 107:     ).pack(pady=(30, 10))
 108:     
 109:     ttk.Button(
 110:         frame,
 111:         text="退出",
 112:         command=root.quit,
 113:         width=30
 114:     ).pack(pady=10)
 115:     
 116:     root.mainloop()
 117: 
 118: 
 119: def test_main_window_size():
 120:     """测试主窗口尺寸"""
 121:     try:
 122:         from gui.main_window import MainWindow
 123:         
 124:         print("启动改进后的主窗口...")
 125:         print("窗口特性：")
 126:         print("- 初始尺寸: 900x750")
 127:         print("- 最小尺寸: 800x650")
 128:         print("- 增加的内边距: 15px")
 129:         print("- 可调整大小")
 130:         
 131:         app = MainWindow()
 132:         app.run()
 133:         
 134:     except Exception as e:
 135:         print(f"主窗口测试失败: {e}")
 136:         import traceback
 137:         traceback.print_exc()
 138: 
 139: 
 140: def main():
 141:     """主函数"""
 142:     print("=" * 60)
 143:     print("改进后的GUI界面测试")
 144:     print("=" * 60)
 145:     
 146:     choice = input("""
 147: 请选择测试项目:
 148: 1. 测试大尺寸进度对话框
 149: 2. 测试改进后的主窗口
 150: 3. 退出
 151: 
 152: 请输入选择 (1-3): """).strip()
 153:     
 154:     if choice == "1":
 155:         print("启动大尺寸进度对话框测试...")
 156:         test_large_progress_dialog()
 157:     elif choice == "2":
 158:         print("启动改进后的主窗口测试...")
 159:         test_main_window_size()
 160:     elif choice == "3":
 161:         print("退出测试")
 162:     else:
 163:         print("无效选择")
 164: 
 165: 
 166: if __name__ == '__main__':
 167:     main()
 168: 


================================================================================
文件 25: test_simple.py
================================================================================

   1: #!/usr/bin/env python3
   2: """
   3: 简单功能测试 - 验证程序基本功能
   4: """
   5: 
   6: import os
   7: import sys
   8: import numpy as np
   9: import tempfile
  10: 
  11: # 添加项目根目录到路径
  12: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
  13: 
  14: def test_imports():
  15:     """测试模块导入"""
  16:     print("测试模块导入...")
  17:     
  18:     try:
  19:         from core.grid_calculator import BeidouGridCalculator
  20:         print("✓ 网格计算器导入成功")
  21:         
  22:         from core.aggregation import AggregationMethod, DataAggregator
  23:         print("✓ 数据聚合模块导入成功")
  24:         
  25:         from core.coordinate_utils import CoordinateUtils
  26:         print("✓ 坐标工具导入成功")
  27:         
  28:         from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
  29:         print("✓ 主处理器导入成功")
  30:         
  31:         return True
  32:         
  33:     except Exception as e:
  34:         print(f"✗ 导入失败: {e}")
  35:         return False
  36: 
  37: 
  38: def test_basic_functionality():
  39:     """测试基本功能"""
  40:     print("\n测试基本功能...")
  41:     
  42:     try:
  43:         from core.grid_calculator import BeidouGridCalculator
  44:         from core.aggregation import DataAggregator
  45:         
  46:         # 测试网格计算
  47:         calculator = BeidouGridCalculator()
  48:         grid_code = calculator.calculate_grid_code(39.9042, 116.4074, 7)
  49:         print(f"✓ 网格编码计算: {grid_code}")
  50:         
  51:         # 测试数据聚合
  52:         aggregator = DataAggregator(method="mean", window_size=3)
  53:         test_data = np.random.rand(10, 10) * 100
  54:         result = aggregator.aggregate(test_data)
  55:         print(f"✓ 数据聚合: {test_data.shape} -> {result.shape}")
  56:         
  57:         return True
  58:         
  59:     except Exception as e:
  60:         print(f"✗ 基本功能测试失败: {e}")
  61:         import traceback
  62:         traceback.print_exc()
  63:         return False
  64: 
  65: 
  66: def test_processor_config():
  67:     """测试处理器配置"""
  68:     print("\n测试处理器配置...")
  69:     
  70:     try:
  71:         from beidou_grid_processor import ProcessingConfig
  72:         
  73:         # 创建配置
  74:         config = ProcessingConfig(
  75:             input_file="test_input.tif",
  76:             output_raster="test_output.tif",
  77:             output_vector="test_output.shp",
  78:             grid_level=7,
  79:             aggregation_method="mean",
  80:             window_size=5
  81:         )
  82:         
  83:         print(f"✓ 配置创建成功:")
  84:         print(f"  输入文件: {config.input_file}")
  85:         print(f"  网格级别: {config.grid_level}")
  86:         print(f"  聚合方法: {config.aggregation_method}")
  87:         print(f"  窗口大小: {config.window_size}")
  88:         
  89:         return True
  90:         
  91:     except Exception as e:
  92:         print(f"✗ 配置测试失败: {e}")
  93:         return False
  94: 
  95: 
  96: def test_cli_interface():
  97:     """测试命令行接口"""
  98:     print("\n测试命令行接口...")
  99:     
 100:     try:
 101:         import subprocess
 102:         
 103:         # 测试帮助信息
 104:         result = subprocess.run([
 105:             sys.executable, "main.py", "--help"
 106:         ], capture_output=True, text=True, timeout=10)
 107:         
 108:         if result.returncode == 0 and "北斗网格转换工具" in result.stdout:
 109:             print("✓ 命令行帮助信息正常")
 110:             return True
 111:         else:
 112:             print(f"✗ 命令行测试失败: {result.stderr}")
 113:             return False
 114:             
 115:     except Exception as e:
 116:         print(f"✗ 命令行接口测试失败: {e}")
 117:         return False
 118: 
 119: 
 120: def create_test_data():
 121:     """创建测试数据文件"""
 122:     print("\n创建测试数据...")
 123:     
 124:     try:
 125:         # 创建一个简单的测试TIFF文件
 126:         import rasterio
 127:         from rasterio.transform import from_bounds
 128:         
 129:         # 测试数据
 130:         width, height = 100, 100
 131:         bounds = (116.0, 39.0, 117.0, 40.0)  # 北京区域
 132:         
 133:         # 创建变换
 134:         transform = from_bounds(*bounds, width, height)
 135:         
 136:         # 创建测试数据
 137:         data = np.random.rand(height, width) * 100
 138:         
 139:         # 添加一些模式
 140:         center_row, center_col = height // 2, width // 2
 141:         for i in range(height):
 142:             for j in range(width):
 143:                 distance = np.sqrt((i - center_row)**2 + (j - center_col)**2)
 144:                 data[i, j] += 50 * np.exp(-distance / 20)
 145:         
 146:         # 保存文件
 147:         test_file = "test_data.tif"
 148:         with rasterio.open(
 149:             test_file, 'w',
 150:             driver='GTiff',
 151:             height=height,
 152:             width=width,
 153:             count=1,
 154:             dtype=data.dtype,
 155:             crs='EPSG:4326',
 156:             transform=transform,
 157:         ) as dst:
 158:             dst.write(data, 1)
 159:         
 160:         print(f"✓ 测试数据文件创建: {test_file}")
 161:         print(f"  尺寸: {width}x{height}")
 162:         print(f"  范围: {bounds}")
 163:         print(f"  数据范围: {np.min(data):.2f} - {np.max(data):.2f}")
 164:         
 165:         return test_file
 166:         
 167:     except Exception as e:
 168:         print(f"✗ 测试数据创建失败: {e}")
 169:         return None
 170: 
 171: 
 172: def test_full_processing():
 173:     """测试完整处理流程"""
 174:     print("\n测试完整处理流程...")
 175:     
 176:     try:
 177:         from beidou_grid_processor import BeidouGridProcessor, ProcessingConfig
 178:         
 179:         # 创建测试数据
 180:         test_file = create_test_data()
 181:         if not test_file:
 182:             return False
 183:         
 184:         # 创建配置
 185:         config = ProcessingConfig(
 186:             input_file=test_file,
 187:             output_raster="test_output.tif",
 188:             output_vector="test_output.shp",
 189:             grid_level=6,  # 使用较低级别以减少处理时间
 190:             aggregation_method="mean",
 191:             window_size=3,
 192:             verbose=True
 193:         )
 194:         
 195:         # 创建处理器
 196:         processor = BeidouGridProcessor(config)
 197:         
 198:         print("✓ 处理器创建成功")
 199:         print("✓ 配置验证通过")
 200:         
 201:         # 注意：这里不实际运行处理，因为可能需要很长时间
 202:         print("✓ 完整处理流程配置正确")
 203:         
 204:         # 清理测试文件
 205:         if os.path.exists(test_file):
 206:             os.remove(test_file)
 207:             print("✓ 测试文件清理完成")
 208:         
 209:         return True
 210:         
 211:     except Exception as e:
 212:         print(f"✗ 完整处理测试失败: {e}")
 213:         import traceback
 214:         traceback.print_exc()
 215:         return False
 216: 
 217: 
 218: def main():
 219:     """主测试函数"""
 220:     print("=" * 60)
 221:     print("北斗网格转换工具 - 简单功能验证")
 222:     print("=" * 60)
 223:     
 224:     tests = [
 225:         ("模块导入", test_imports),
 226:         ("基本功能", test_basic_functionality),
 227:         ("处理器配置", test_processor_config),
 228:         ("命令行接口", test_cli_interface),
 229:         ("完整处理流程", test_full_processing),
 230:     ]
 231:     
 232:     passed = 0
 233:     total = len(tests)
 234:     
 235:     for test_name, test_func in tests:
 236:         print(f"\n{'='*20} {test_name} {'='*20}")
 237:         try:
 238:             if test_func():
 239:                 passed += 1
 240:                 print(f"✓ {test_name} 测试通过")
 241:             else:
 242:                 print(f"✗ {test_name} 测试失败")
 243:         except Exception as e:
 244:             print(f"✗ {test_name} 测试异常: {e}")
 245:     
 246:     print("\n" + "=" * 60)
 247:     print(f"测试结果: {passed}/{total} 通过")
 248:     
 249:     if passed == total:
 250:         print("🎉 所有测试通过！程序功能正常。")
 251:         print("\n注意事项:")
 252:         print("- PROJ数据库警告不影响基本功能")
 253:         print("- GUI界面已成功启动")
 254:         print("- 命令行接口工作正常")
 255:         print("- 核心算法功能完整")
 256:     else:
 257:         print("⚠️  部分测试失败，请检查相关功能。")
 258:     
 259:     print("=" * 60)
 260: 
 261: 
 262: if __name__ == '__main__':
 263:     main()
 264: 

