"""
矢量数据写入模块
"""

import os
import numpy as np
import fiona
from fiona.crs import from_epsg
from shapely.geometry import Polygon, mapping
from typing import List, Dict, Any, Optional, Tuple
import logging


class VectorWriter:
    """矢量数据写入器"""
    
    def __init__(self, file_path: str, crs: Optional[Any] = None):
        """
        初始化写入器
        
        Args:
            file_path: 输出文件路径
            crs: 坐标参考系统
        """
        self.file_path = file_path
        self.crs = crs or from_epsg(4326)  # 默认使用WGS84
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    def write_grid_polygons(self, grid_data: np.ndarray,
                           min_lon: float, min_lat: float,
                           max_lon: float, max_lat: float,
                           grid_level: int,
                           grid_calculator,
                           additional_fields: Optional[Dict[str, Any]] = None,
                           progress_callback=None) -> bool:
        """
        将网格数据写入矢量文件
        
        Args:
            grid_data: 网格数据数组
            min_lon, min_lat, max_lon, max_lat: 边界坐标
            grid_level: 网格级别
            grid_calculator: 网格计算器实例
            additional_fields: 额外字段
            
        Returns:
            是否成功
        """
        try:
            # 获取网格尺寸
            lon_size, lat_size = grid_calculator.get_grid_size(grid_level)
            rows, cols = grid_data.shape

            # 计算有效要素数量（用于进度显示）
            valid_count = np.sum(~np.isnan(grid_data) & (grid_data != 0))
            if progress_callback:
                progress_callback(90, "开始写入矢量文件", f"预计要素数量: {valid_count}")

            processed_count = 0
            
            # 定义schema
            schema = {
                'geometry': 'Polygon',
                'properties': {
                    'grid_code': 'str',
                    'grid_level': 'int',
                    'value': 'float',
                    'row': 'int',
                    'col': 'int',
                    'center_lon': 'float',
                    'center_lat': 'float',
                    'area_sqm': 'float'
                }
            }
            
            # 添加额外字段
            if additional_fields:
                schema['properties'].update(additional_fields)
            
            # 写入文件
            with fiona.open(self.file_path, 'w', 
                          driver='ESRI Shapefile',
                          crs=self.crs,
                          schema=schema) as output:
                
                for i in range(rows):
                    for j in range(cols):
                        value = grid_data[i, j]

                        # 跳过无效值
                        if np.isnan(value) or value == 0:
                            continue

                        processed_count += 1
                        
                        # 计算网格中心坐标
                        center_lon = min_lon + (j + 0.5) * lon_size
                        center_lat = min_lat + (i + 0.5) * lat_size
                        
                        # 计算网格边界
                        grid_min_lon = min_lon + j * lon_size
                        grid_max_lon = min_lon + (j + 1) * lon_size
                        grid_min_lat = min_lat + i * lat_size
                        grid_max_lat = min_lat + (i + 1) * lat_size
                        
                        # 创建多边形
                        polygon = Polygon([
                            (grid_min_lon, grid_min_lat),
                            (grid_max_lon, grid_min_lat),
                            (grid_max_lon, grid_max_lat),
                            (grid_min_lon, grid_max_lat),
                            (grid_min_lon, grid_min_lat)
                        ])
                        
                        # 计算网格编码
                        grid_code = grid_calculator.calculate_grid_code(
                            center_lat, center_lon, grid_level)
                        
                        # 计算面积
                        from core.coordinate_utils import CoordinateUtils
                        area = CoordinateUtils.calculate_grid_area(center_lat, grid_level)
                        
                        # 创建要素
                        feature = {
                            'geometry': mapping(polygon),
                            'properties': {
                                'grid_code': grid_code,
                                'grid_level': grid_level,
                                'value': float(value),
                                'row': i,
                                'col': j,
                                'center_lon': center_lon,
                                'center_lat': center_lat,
                                'area_sqm': area
                            }
                        }
                        
                        output.write(feature)

                        # 更新进度（每100个要素更新一次）
                        if progress_callback and processed_count % 100 == 0:
                            progress = 90 + int((processed_count / valid_count) * 5)  # 90-95%
                            progress_callback(progress, "写入矢量要素", f"已处理: {processed_count}/{valid_count}")
            
            logging.info(f"成功写入矢量文件: {self.file_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入矢量文件失败: {str(e)}")
            return False
    
    def write_polygons_from_coordinates(self, polygons_data: List[Dict[str, Any]]) -> bool:
        """
        从坐标数据写入多边形
        
        Args:
            polygons_data: 多边形数据列表，每个元素包含 'coordinates' 和 'properties'
            
        Returns:
            是否成功
        """
        try:
            if not polygons_data:
                logging.warning("没有数据要写入")
                return False
            
            # 从第一个多边形推断schema
            first_polygon = polygons_data[0]
            properties = first_polygon.get('properties', {})
            
            schema = {
                'geometry': 'Polygon',
                'properties': {}
            }
            
            # 推断属性类型
            for key, value in properties.items():
                if isinstance(value, str):
                    schema['properties'][key] = 'str'
                elif isinstance(value, int):
                    schema['properties'][key] = 'int'
                elif isinstance(value, float):
                    schema['properties'][key] = 'float'
                else:
                    schema['properties'][key] = 'str'
            
            # 写入文件
            with fiona.open(self.file_path, 'w',
                          driver='ESRI Shapefile',
                          crs=self.crs,
                          schema=schema) as output:
                
                for polygon_data in polygons_data:
                    coordinates = polygon_data['coordinates']
                    properties = polygon_data.get('properties', {})
                    
                    # 创建多边形
                    polygon = Polygon(coordinates)
                    
                    # 创建要素
                    feature = {
                        'geometry': mapping(polygon),
                        'properties': properties
                    }
                    
                    output.write(feature)
            
            logging.info(f"成功写入 {len(polygons_data)} 个多边形到: {self.file_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入多边形失败: {str(e)}")
            return False
    
    def raster_to_polygons(self, raster_array: np.ndarray,
                          transform: Any,
                          mask_value: Optional[float] = None,
                          progress_callback=None) -> bool:
        """
        将栅格转换为多边形
        
        Args:
            raster_array: 栅格数组
            transform: 仿射变换参数
            mask_value: 掩码值（不转换的值）
            
        Returns:
            是否成功
        """
        try:
            import rasterio.features

            if progress_callback:
                progress_callback(90, "开始提取多边形", "分析栅格数据...")

            # 创建掩码
            if mask_value is not None:
                mask = raster_array != mask_value
            else:
                mask = np.ones_like(raster_array, dtype=bool)

            if progress_callback:
                progress_callback(92, "提取多边形", "正在矢量化栅格数据...")

            # 提取多边形
            shapes = list(rasterio.features.shapes(
                raster_array.astype(np.int32),
                mask=mask,
                transform=transform
            ))

            total_shapes = len(shapes)
            if progress_callback:
                progress_callback(93, "写入多边形", f"共提取到 {total_shapes} 个多边形")
            
            # 定义schema
            schema = {
                'geometry': 'Polygon',
                'properties': {
                    'value': 'int'
                }
            }
            
            # 写入文件
            with fiona.open(self.file_path, 'w',
                          driver='ESRI Shapefile',
                          crs=self.crs,
                          schema=schema) as output:

                processed = 0
                for geom, value in shapes:
                    if value != mask_value:  # 跳过掩码值
                        feature = {
                            'geometry': geom,
                            'properties': {
                                'value': int(value)
                            }
                        }
                        output.write(feature)
                        processed += 1

                        # 更新进度（每50个多边形更新一次）
                        if progress_callback and processed % 50 == 0:
                            progress = 93 + int((processed / total_shapes) * 2)  # 93-95%
                            progress_callback(progress, "写入多边形", f"已写入: {processed}/{total_shapes}")
            
            logging.info(f"成功将栅格转换为多边形: {self.file_path}")
            return True
            
        except Exception as e:
            logging.error(f"栅格转多边形失败: {str(e)}")
            return False
    
    @staticmethod
    def create_grid_shapefile(output_path: str, 
                            min_lon: float, min_lat: float,
                            max_lon: float, max_lat: float,
                            grid_level: int,
                            grid_calculator,
                            crs: Optional[Any] = None) -> bool:
        """
        创建网格矢量文件（不包含数据值）
        
        Args:
            output_path: 输出路径
            min_lon, min_lat, max_lon, max_lat: 边界
            grid_level: 网格级别
            grid_calculator: 网格计算器
            crs: 坐标系
            
        Returns:
            是否成功
        """
        writer = VectorWriter(output_path, crs)
        
        # 获取网格尺寸
        lon_size, lat_size = grid_calculator.get_grid_size(grid_level)
        
        # 计算网格数量
        cols = int(np.ceil((max_lon - min_lon) / lon_size))
        rows = int(np.ceil((max_lat - min_lat) / lat_size))
        
        # 创建空的网格数据（全部为1，表示有效网格）
        grid_data = np.ones((rows, cols))
        
        return writer.write_grid_polygons(
            grid_data, min_lon, min_lat, max_lon, max_lat,
            grid_level, grid_calculator
        )
