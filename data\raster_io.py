"""
栅格数据读写模块
使用rasterio替代ArcPy
"""

import os
import numpy as np
import rasterio
from rasterio.transform import from_bounds
from rasterio.enums import Resampling
from rasterio.warp import calculate_default_transform, reproject
from typing import Tuple, Optional, Dict, Any, Union
import logging


class RasterReader:
    """栅格数据读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化读取器
        
        Args:
            file_path: 栅格文件路径
        """
        self.file_path = file_path
        self._dataset = None
        self._array = None
        self._transform = None
        self._crs = None
        self._nodata = None
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.open()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def open(self):
        """打开栅格文件"""
        try:
            self._dataset = rasterio.open(self.file_path)
            self._transform = self._dataset.transform
            self._crs = self._dataset.crs
            self._nodata = self._dataset.nodata
            logging.info(f"成功打开栅格文件: {self.file_path}")
        except Exception as e:
            raise RuntimeError(f"无法打开栅格文件 {self.file_path}: {str(e)}")
    
    def close(self):
        """关闭栅格文件"""
        if self._dataset:
            self._dataset.close()
            self._dataset = None
    
    def read_array(self, band: int = 1, window: Optional[rasterio.windows.Window] = None,
                   masked: bool = True) -> np.ndarray:
        """
        读取栅格数据为numpy数组
        
        Args:
            band: 波段号（从1开始）
            window: 读取窗口
            masked: 是否返回掩码数组
            
        Returns:
            numpy数组
        """
        if not self._dataset:
            self.open()
        
        try:
            array = self._dataset.read(band, window=window, masked=masked)
            
            # 处理无效值
            if not masked and self._nodata is not None:
                array = np.where(array == self._nodata, np.nan, array)
            
            self._array = array
            logging.info(f"成功读取数组，形状: {array.shape}")
            return array
            
        except Exception as e:
            raise RuntimeError(f"读取栅格数据失败: {str(e)}")
    
    def get_bounds(self) -> Tuple[float, float, float, float]:
        """
        获取栅格边界
        
        Returns:
            (min_x, min_y, max_x, max_y)
        """
        if not self._dataset:
            self.open()
        
        return self._dataset.bounds
    
    def get_transform(self) -> rasterio.transform.Affine:
        """获取仿射变换参数"""
        if not self._dataset:
            self.open()
        
        return self._transform
    
    def get_crs(self):
        """获取坐标参考系统"""
        if not self._dataset:
            self.open()
        
        return self._crs
    
    def get_shape(self) -> Tuple[int, int]:
        """获取栅格形状"""
        if not self._dataset:
            self.open()
        
        return self._dataset.height, self._dataset.width
    
    def get_nodata_value(self) -> Optional[float]:
        """获取无效值"""
        if not self._dataset:
            self.open()
        
        return self._nodata
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取元数据"""
        if not self._dataset:
            self.open()
        
        return {
            'driver': self._dataset.driver,
            'dtype': self._dataset.dtypes[0],
            'nodata': self._dataset.nodata,
            'width': self._dataset.width,
            'height': self._dataset.height,
            'count': self._dataset.count,
            'crs': self._dataset.crs,
            'transform': self._dataset.transform,
            'bounds': self._dataset.bounds
        }
    
    def read_window(self, row_start: int, row_stop: int, 
                   col_start: int, col_stop: int, band: int = 1) -> np.ndarray:
        """
        读取指定窗口的数据
        
        Args:
            row_start, row_stop: 行范围
            col_start, col_stop: 列范围
            band: 波段号
            
        Returns:
            窗口数据
        """
        window = rasterio.windows.Window(col_start, row_start, 
                                       col_stop - col_start, 
                                       row_stop - row_start)
        return self.read_array(band=band, window=window)
    
    def sample_points(self, coordinates: list, band: int = 1) -> list:
        """
        在指定坐标点采样
        
        Args:
            coordinates: 坐标点列表 [(x, y), ...]
            band: 波段号
            
        Returns:
            采样值列表
        """
        if not self._dataset:
            self.open()
        
        try:
            samples = list(self._dataset.sample(coordinates, indexes=band))
            return [sample[0] for sample in samples]
        except Exception as e:
            raise RuntimeError(f"坐标采样失败: {str(e)}")


class RasterWriter:
    """栅格数据写入器"""
    
    def __init__(self, file_path: str, array: np.ndarray, 
                 transform: rasterio.transform.Affine,
                 crs: Optional[Any] = None,
                 nodata: Optional[float] = None,
                 dtype: Optional[str] = None,
                 compress: str = 'lzw'):
        """
        初始化写入器
        
        Args:
            file_path: 输出文件路径
            array: 要写入的数组
            transform: 仿射变换参数
            crs: 坐标参考系统
            nodata: 无效值
            dtype: 数据类型
            compress: 压缩方式
        """
        self.file_path = file_path
        self.array = array
        self.transform = transform
        self.crs = crs
        self.nodata = nodata
        self.dtype = dtype or array.dtype
        self.compress = compress
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    def write(self, create_overview: bool = True) -> bool:
        """
        写入栅格文件
        
        Args:
            create_overview: 是否创建概览图
            
        Returns:
            是否成功
        """
        try:
            # 确定数组维度
            if self.array.ndim == 2:
                height, width = self.array.shape
                count = 1
                array_to_write = self.array[np.newaxis, :, :]  # 添加波段维度
            elif self.array.ndim == 3:
                count, height, width = self.array.shape
                array_to_write = self.array
            else:
                raise ValueError(f"不支持的数组维度: {self.array.ndim}")
            
            # 设置写入参数
            profile = {
                'driver': 'GTiff',
                'dtype': self.dtype,
                'nodata': self.nodata,
                'width': width,
                'height': height,
                'count': count,
                'crs': self.crs,
                'transform': self.transform,
                'compress': self.compress,
                'tiled': True,
                'blockxsize': 512,
                'blockysize': 512
            }
            
            # 写入文件
            with rasterio.open(self.file_path, 'w', **profile) as dst:
                dst.write(array_to_write)
                
                # 创建概览图
                if create_overview:
                    factors = [2, 4, 8, 16]
                    dst.build_overviews(factors, Resampling.average)
                    dst.update_tags(ns='rio_overview', resampling='average')
            
            logging.info(f"成功写入栅格文件: {self.file_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入栅格文件失败: {str(e)}")
            return False
    
    @staticmethod
    def create_from_bounds(min_x: float, min_y: float, max_x: float, max_y: float,
                          width: int, height: int, crs: Optional[Any] = None) -> rasterio.transform.Affine:
        """
        根据边界创建仿射变换参数
        
        Args:
            min_x, min_y, max_x, max_y: 边界坐标
            width, height: 栅格尺寸
            crs: 坐标参考系统
            
        Returns:
            仿射变换参数
        """
        return from_bounds(min_x, min_y, max_x, max_y, width, height)
    
    @staticmethod
    def array_to_raster(array: np.ndarray, file_path: str,
                       min_x: float, min_y: float, max_x: float, max_y: float,
                       crs: Optional[Any] = None, nodata: Optional[float] = None) -> bool:
        """
        便捷方法：将数组直接写入栅格文件
        
        Args:
            array: 数组
            file_path: 输出路径
            min_x, min_y, max_x, max_y: 边界
            crs: 坐标系
            nodata: 无效值
            
        Returns:
            是否成功
        """
        height, width = array.shape
        transform = RasterWriter.create_from_bounds(min_x, min_y, max_x, max_y, width, height)
        
        writer = RasterWriter(file_path, array, transform, crs, nodata)
        return writer.write()
