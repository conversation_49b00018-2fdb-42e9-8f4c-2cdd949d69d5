"""
进度对话框
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import logging
from .window_utils import center_window


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent):
        """
        初始化进度对话框
        
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.dialog = None
        self.progress_var = tk.IntVar()
        self.message_var = tk.StringVar()
        self.is_cancelled = False
        self.cancel_callback = None

        # 时间跟踪
        self.start_time = time.time()
        self.stage_start_time = time.time()
        self.stage_progress_history = []  # 存储每个阶段的进度历史
        self.current_stage = ""
        self.last_percent = 0
        self.last_eta = 0  # 上次预估的剩余时间
        
        # 创建对话框
        self._create_dialog()
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("处理进度")

        # 居中显示对话框
        center_window(self.dialog, 700, 400)
        self.dialog.minsize(600, 350)    # 设置最小尺寸
        self.dialog.resizable(True, True)  # 允许调整大小

        # 设置为模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 创建界面组件
        self._create_widgets()
        self._setup_layout()
        
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # 初始隐藏
        self.dialog.withdraw()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        
        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 获取对话框大小
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.dialog, padding="30")

        # 标题
        self.title_label = ttk.Label(
            self.main_frame,
            text="正在处理，请稍候...",
            font=("Arial", 16, "bold")
        )

        # 进度条
        self.progress_bar = ttk.Progressbar(
            self.main_frame,
            variable=self.progress_var,
            maximum=100,
            length=600,  # 进一步增加进度条长度
            mode='determinate'
        )

        # 进度百分比
        self.percent_label = ttk.Label(
            self.main_frame,
            text="0%",
            font=("Arial", 14, "bold")
        )

        # 状态消息
        self.message_label = ttk.Label(
            self.main_frame,
            textvariable=self.message_var,
            font=("Arial", 12),
            foreground="blue",
            wraplength=600  # 增加自动换行宽度
        )
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            self.main_frame,
            text="取消处理",
            command=self._on_cancel,
            width=15
        )
        
        # 时间显示
        self.time_var = tk.StringVar()
        self.time_label = ttk.Label(
            self.main_frame,
            textvariable=self.time_var,
            font=("Arial", 11),
            foreground="gray"
        )

        # 预计剩余时间显示
        self.eta_var = tk.StringVar()
        self.eta_label = ttk.Label(
            self.main_frame,
            textvariable=self.eta_var,
            font=("Arial", 11),
            foreground="orange"
        )

        # 详细信息显示
        self.detail_var = tk.StringVar()
        self.detail_label = ttk.Label(
            self.main_frame,
            textvariable=self.detail_var,
            font=("Arial", 11),
            foreground="darkgreen",
            wraplength=600  # 增加自动换行宽度
        )
        
        # 初始化时间
        self.start_time = None
        self._update_time()
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        self.title_label.pack(pady=(0, 25))
        self.progress_bar.pack(pady=(0, 15))

        # 进度百分比和时间在同一行
        percent_time_frame = ttk.Frame(self.main_frame)
        percent_time_frame.pack(fill=tk.X, pady=(0, 10))

        self.percent_label.pack(in_=percent_time_frame, side=tk.LEFT)
        self.time_label.pack(in_=percent_time_frame, side=tk.RIGHT)

        # 预计剩余时间
        self.eta_label.pack(pady=(0, 15))

        # 状态消息
        self.message_label.pack(pady=(0, 15), fill=tk.X)

        # 详细信息
        self.detail_label.pack(pady=(0, 20), fill=tk.X)

        # 取消按钮
        self.cancel_button.pack(pady=(10, 0))
    
    def show(self):
        """显示对话框"""
        self.start_time = time.time()
        self.dialog.deiconify()
        self.dialog.lift()
        self.dialog.focus_set()
        
        # 开始更新时间
        self._schedule_time_update()
    
    def close(self):
        """关闭对话框"""
        if self.dialog:
            self.dialog.grab_release()
            self.dialog.destroy()
            self.dialog = None
    
    def update_progress(self, percent: int, message: str = "", detail: str = "", extra_info: dict = None):
        """
        更新进度

        Args:
            percent: 进度百分比 (0-100)
            message: 状态消息
            detail: 详细信息
            extra_info: 额外信息（用于更精确的时间估算）
        """
        if self.dialog and self.dialog.winfo_exists():
            self.progress_var.set(percent)
            self.percent_label.config(text=f"{percent}%")

            if message:
                self.message_var.set(message)
                # 检查是否是新阶段
                if message != self.current_stage:
                    self.current_stage = message
                    self.stage_start_time = time.time()
                    self.stage_progress_history = []

            if detail:
                self.detail_var.set(detail)

            # 更新时间估算
            self._update_time_estimation(percent, extra_info)

            # 如果进度达到100%，禁用取消按钮
            if percent >= 100:
                self.cancel_button.config(state="disabled", text="处理完成")
                self.eta_var.set("处理完成！")

            self.last_percent = percent
            # 更新界面
            self.dialog.update_idletasks()
    
    def set_cancel_callback(self, callback):
        """
        设置取消回调函数
        
        Args:
            callback: 取消时调用的函数
        """
        self.cancel_callback = callback
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.is_cancelled = True
        self.cancel_button.config(state="disabled", text="正在取消...")

        if self.cancel_callback:
            # 立即执行取消操作
            try:
                self.cancel_callback()
                # 取消成功后立即关闭对话框
                self.dialog.after(100, self.close)
            except Exception as e:
                logging.error(f"取消操作失败: {e}")
                # 即使取消失败也关闭对话框
                self.dialog.after(100, self.close)
    
    def _on_close(self):
        """对话框关闭事件"""
        # 阻止直接关闭，只能通过取消按钮
        pass
    
    def _update_time(self):
        """更新时间显示"""
        if self.start_time and self.dialog and self.dialog.winfo_exists():
            elapsed = time.time() - self.start_time
            
            if elapsed < 60:
                time_str = f"已用时: {elapsed:.0f}秒"
            elif elapsed < 3600:
                minutes = int(elapsed // 60)
                seconds = int(elapsed % 60)
                time_str = f"已用时: {minutes}分{seconds}秒"
            else:
                hours = int(elapsed // 3600)
                minutes = int((elapsed % 3600) // 60)
                time_str = f"已用时: {hours}小时{minutes}分"
            
            self.time_var.set(time_str)

    def _update_time_estimation(self, percent: int, extra_info: dict = None):
        """更新时间估算"""
        if percent <= 0 or not self.start_time:
            return

        current_time = time.time()

        # 记录当前阶段的进度历史
        if self.current_stage:
            progress_data = {
                'time': current_time,
                'percent': percent
            }

            # 如果有额外信息（如像素处理信息），添加到历史中
            if extra_info:
                progress_data.update(extra_info)

            self.stage_progress_history.append(progress_data)

            # 只保留最近的8个数据点（减少数据点以提高稳定性）
            if len(self.stage_progress_history) > 8:
                self.stage_progress_history.pop(0)

        # 计算预计剩余时间
        eta_text = self._calculate_eta(percent, current_time, extra_info)
        if eta_text:
            self.eta_var.set(eta_text)

    def _calculate_eta(self, percent: int, current_time: float, extra_info: dict = None) -> str:
        """计算预计剩余时间"""
        if percent <= 10:  # 进度太少时不显示预估
            return ""

        # 如果是数据聚合阶段且有像素处理信息，使用更精确的计算
        if (extra_info and 'pixels_processed' in extra_info and
            'total_pixels' in extra_info and self.current_stage == "执行数据聚合"):
            return self._calculate_pixel_based_eta(extra_info, current_time)

        # 使用最近的进度数据计算，避免早期不稳定的数据影响
        if len(self.stage_progress_history) >= 3:  # 需要至少3个数据点
            # 使用最近3个数据点计算平均速度
            recent_data = self.stage_progress_history[-3:]
            time_span = recent_data[-1]['time'] - recent_data[0]['time']
            progress_span = recent_data[-1]['percent'] - recent_data[0]['percent']

            if time_span > 0 and progress_span > 0:
                # 计算平均速度（每秒进度）
                avg_speed = progress_span / time_span
                remaining_progress = 100 - percent
                remaining_time = remaining_progress / avg_speed

                # 平滑处理，避免剧烈波动
                if self.last_eta > 0:
                    # 使用加权平均，新预估占70%，旧预估占30%
                    remaining_time = remaining_time * 0.7 + self.last_eta * 0.3

                self.last_eta = remaining_time

                if remaining_time > 0 and remaining_time < 7200:  # 限制在2小时内
                    return self._format_time_remaining(remaining_time)

        return ""

    def _calculate_pixel_based_eta(self, extra_info: dict, current_time: float) -> str:
        """基于像素处理进度计算预计剩余时间"""
        pixels_processed = extra_info['pixels_processed']
        total_pixels = extra_info['total_pixels']

        if pixels_processed <= 0:
            return ""

        # 计算聚合阶段开始以来的时间
        aggregation_elapsed = current_time - self.stage_start_time

        if aggregation_elapsed > 0:
            # 计算像素处理速度（每秒处理的像素数）
            pixel_speed = pixels_processed / aggregation_elapsed
            remaining_pixels = total_pixels - pixels_processed

            if pixel_speed > 0:
                remaining_time = remaining_pixels / pixel_speed

                # 平滑处理
                if self.last_eta > 0:
                    remaining_time = remaining_time * 0.8 + self.last_eta * 0.2

                self.last_eta = remaining_time

                if remaining_time > 0 and remaining_time < 3600:  # 限制在1小时内
                    return self._format_time_remaining(remaining_time)

        return ""

    def _calculate_stage_eta(self) -> float:
        """基于当前阶段的进度历史计算预估时间"""
        if len(self.stage_progress_history) < 3:
            return 0

        # 计算最近几个数据点的平均速度
        recent_data = self.stage_progress_history[-3:]
        time_diff = recent_data[-1]['time'] - recent_data[0]['time']
        progress_diff = recent_data[-1]['percent'] - recent_data[0]['percent']

        if time_diff > 0 and progress_diff > 0:
            speed = progress_diff / time_diff  # 每秒进度
            remaining_progress = 100 - recent_data[-1]['percent']
            return remaining_progress / speed

        return 0

    def _format_time_remaining(self, seconds: float) -> str:
        """格式化剩余时间显示"""
        seconds = int(seconds)

        if seconds < 60:
            return f"预计剩余: {seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"预计剩余: {minutes}分{secs}秒"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"预计剩余: {hours}小时{minutes}分钟"

    def _schedule_time_update(self):
        """定时更新时间"""
        if self.dialog and self.dialog.winfo_exists():
            self._update_time()
            # 每秒更新一次
            self.dialog.after(1000, self._schedule_time_update)


class IndeterminateProgressDialog(ProgressDialog):
    """不确定进度的对话框"""
    
    def _create_widgets(self):
        """创建界面组件"""
        super()._create_widgets()
        
        # 修改进度条为不确定模式
        self.progress_bar.config(mode='indeterminate')
        
        # 隐藏百分比标签
        self.percent_label.pack_forget()
    
    def show(self):
        """显示对话框"""
        super().show()
        # 开始不确定进度动画
        self.progress_bar.start(10)
    
    def close(self):
        """关闭对话框"""
        if self.progress_bar:
            self.progress_bar.stop()
        super().close()
    
    def update_progress(self, percent: int = 0, message: str = "", detail: str = ""):
        """
        更新进度（忽略百分比）

        Args:
            percent: 忽略
            message: 状态消息
            detail: 详细信息
        """
        if self.dialog and self.dialog.winfo_exists():
            if message:
                self.message_var.set(message)

            if detail:
                self.detail_var.set(detail)
            
            # 更新界面
            self.dialog.update_idletasks()
